# Deployment Guide - Social Media Engagement Platform

## 🚀 Production Deployment

This guide covers deploying the Social Media Engagement Platform to production using Docker and cloud services.

## Prerequisites

- Docker and Docker Compose installed
- Domain name configured
- SSL certificates (Let's Encrypt recommended)
- Cloud database (PostgreSQL)
- Redis instance
- Stripe account for payments
- Email service (SMTP)

## Environment Setup

### 1. Production Environment Variables

Create a `.env.production` file:

```bash
# Application
NODE_ENV=production
DEBUG=false
APP_NAME="Social Engagement Platform"
DOMAIN=yourdomain.com

# Database (use cloud PostgreSQL)
DATABASE_URL=************************************************/social_engagement

# Redis (use cloud Redis)
REDIS_URL=redis://your-redis-host:6379

# Security
JWT_SECRET_KEY=your-super-secure-jwt-secret-key-min-32-chars
STRIPE_SECRET_KEY=sk_live_your_live_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email (production SMTP)
EMAIL_HOST=smtp.youremailprovider.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>

# OAuth (production keys)
GOOGLE_CLIENT_ID=your-production-google-client-id
GOOGLE_CLIENT_SECRET=your-production-google-client-secret
FACEBOOK_APP_ID=your-production-facebook-app-id
FACEBOOK_APP_SECRET=your-production-facebook-app-secret

# URLs
FRONTEND_URL=https://yourdomain.com
BACKEND_URL=https://api.yourdomain.com

# Security headers
SECURE_SSL_REDIRECT=true
SECURE_HSTS_SECONDS=31536000
CORS_ORIGINS=["https://yourdomain.com"]

# Monitoring
SENTRY_DSN=your-sentry-dsn-for-error-tracking
```

### 2. Production Docker Compose

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: social_backend_prod
    env_file:
      - .env.production
    ports:
      - "8000:8000"
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - social_network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: social_frontend_prod
    env_file:
      - .env.production
    ports:
      - "3000:3000"
    restart: unless-stopped
    depends_on:
      - backend
    networks:
      - social_network

  nginx:
    image: nginx:alpine
    container_name: social_nginx_prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - /etc/letsencrypt:/etc/letsencrypt:ro
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    networks:
      - social_network

networks:
  social_network:
    driver: bridge
```

### 3. Nginx Configuration

Create `nginx/nginx.prod.conf`:

```nginx
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server backend:8000;
    }

    upstream frontend {
        server frontend:3000;
    }

    # Redirect HTTP to HTTPS
    server {
        listen 80;
        server_name yourdomain.com api.yourdomain.com;
        return 301 https://$server_name$request_uri;
    }

    # Frontend
    server {
        listen 443 ssl http2;
        server_name yourdomain.com;

        ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

        # Security headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-XSS-Protection "1; mode=block" always;

        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Backend API
    server {
        listen 443 ssl http2;
        server_name api.yourdomain.com;

        ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
        ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;

        # Security headers
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        add_header X-Frame-Options DENY always;
        add_header X-Content-Type-Options nosniff always;

        location / {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Increase timeout for long-running requests
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
        }

        # Stripe webhooks
        location /api/v1/payments/webhook {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Don't buffer webhook requests
            proxy_buffering off;
        }
    }
}
```

## Cloud Deployment Options

### Option 1: AWS Deployment

#### 1. RDS PostgreSQL Setup
```bash
# Create RDS PostgreSQL instance
aws rds create-db-instance \
    --db-instance-identifier social-engagement-db \
    --db-instance-class db.t3.micro \
    --engine postgres \
    --master-username admin \
    --master-user-password your-secure-password \
    --allocated-storage 20 \
    --vpc-security-group-ids sg-xxxxxxxxx
```

#### 2. ElastiCache Redis Setup
```bash
# Create ElastiCache Redis cluster
aws elasticache create-cache-cluster \
    --cache-cluster-id social-engagement-redis \
    --cache-node-type cache.t3.micro \
    --engine redis \
    --num-cache-nodes 1
```

#### 3. ECS Deployment
Create `ecs-task-definition.json` and deploy using AWS ECS.

### Option 2: DigitalOcean Deployment

#### 1. Droplet Setup
```bash
# Create droplet
doctl compute droplet create social-engagement \
    --size s-2vcpu-2gb \
    --image ubuntu-20-04-x64 \
    --region nyc1 \
    --ssh-keys your-ssh-key-id
```

#### 2. Database Setup
```bash
# Create managed PostgreSQL database
doctl databases create social-engagement-db \
    --engine pg \
    --size db-s-1vcpu-1gb \
    --region nyc1
```

### Option 3: Google Cloud Platform

#### 1. Cloud SQL Setup
```bash
# Create Cloud SQL PostgreSQL instance
gcloud sql instances create social-engagement-db \
    --database-version=POSTGRES_13 \
    --tier=db-f1-micro \
    --region=us-central1
```

#### 2. Cloud Run Deployment
```bash
# Deploy backend to Cloud Run
gcloud run deploy social-engagement-backend \
    --image gcr.io/your-project/social-engagement-backend \
    --platform managed \
    --region us-central1 \
    --allow-unauthenticated
```

## SSL Certificate Setup

### Using Let's Encrypt (Recommended)

```bash
# Install certbot
sudo apt-get update
sudo apt-get install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d api.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## Database Migration

```bash
# Run database migrations
docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head

# Create initial admin user (optional)
docker-compose -f docker-compose.prod.yml exec backend python -c "
from app.core.database import AsyncSessionLocal
from app.models.user import User, UserRole, UserStatus
from app.core.security import get_password_hash
import asyncio

async def create_admin():
    async with AsyncSessionLocal() as db:
        admin = User(
            email='<EMAIL>',
            password_hash=get_password_hash('secure_admin_password'),
            first_name='Admin',
            last_name='User',
            username='admin',
            role=UserRole.ADMIN,
            status=UserStatus.ACTIVE,
            email_verified=True
        )
        db.add(admin)
        await db.commit()
        print('Admin user created')

asyncio.run(create_admin())
"
```

## Monitoring & Logging

### 1. Application Monitoring
```bash
# Set up Sentry for error tracking
# Add SENTRY_DSN to environment variables

# Set up health checks
curl https://api.yourdomain.com/health
```

### 2. Infrastructure Monitoring
```bash
# Install monitoring tools
docker run -d \
  --name=prometheus \
  -p 9090:9090 \
  prom/prometheus

docker run -d \
  --name=grafana \
  -p 3001:3000 \
  grafana/grafana
```

### 3. Log Management
```bash
# Configure log rotation
sudo nano /etc/logrotate.d/docker-containers

# Content:
/var/lib/docker/containers/*/*.log {
  rotate 7
  daily
  compress
  size=1M
  missingok
  delaycompress
  copytruncate
}
```

## Security Checklist

- [ ] SSL certificates installed and configured
- [ ] Environment variables secured (no secrets in code)
- [ ] Database access restricted to application only
- [ ] Regular security updates scheduled
- [ ] Backup strategy implemented
- [ ] Rate limiting configured
- [ ] CORS properly configured
- [ ] Security headers implemented
- [ ] Webhook endpoints secured
- [ ] Admin access restricted

## Backup Strategy

### Database Backup
```bash
# Daily database backup
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump $DATABASE_URL > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
rm backup_$DATE.sql
```

### File Backup
```bash
# Backup uploaded files
rsync -av /app/uploads/ s3://your-backup-bucket/uploads/
```

## Scaling Considerations

### Horizontal Scaling
- Use load balancer (AWS ALB, Nginx, Cloudflare)
- Multiple backend instances
- Database read replicas
- CDN for static assets

### Performance Optimization
- Redis caching
- Database query optimization
- Image optimization
- Gzip compression
- HTTP/2 enabled

## Troubleshooting

### Common Issues
1. **Database connection errors**: Check DATABASE_URL and network access
2. **SSL certificate issues**: Verify certificate paths and renewal
3. **Memory issues**: Monitor container resource usage
4. **Slow API responses**: Check database queries and add indexes

### Logs Access
```bash
# View application logs
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f frontend

# View Nginx logs
docker-compose -f docker-compose.prod.yml logs -f nginx
```

## Maintenance

### Regular Tasks
- Update dependencies monthly
- Review security logs weekly
- Monitor performance metrics daily
- Test backup restoration quarterly
- Update SSL certificates (automated with Let's Encrypt)

### Updates
```bash
# Update application
git pull origin main
docker-compose -f docker-compose.prod.yml build
docker-compose -f docker-compose.prod.yml up -d
```

This deployment guide provides a comprehensive approach to deploying the Social Media Engagement Platform in production with security, scalability, and monitoring best practices.
