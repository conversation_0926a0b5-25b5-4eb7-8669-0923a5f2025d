# API Specification - Social Media Engagement Platform

## Base URL
- Development: `http://localhost:8000/api/v1`
- Production: `https://api.socialengagement.com/v1`

## Authentication
All authenticated endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Response Format
All API responses follow this structure:
```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "errors": [],
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid"
  }
}
```

## Error Codes
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `422` - Validation Error
- `429` - Rate Limit Exceeded
- `500` - Internal Server Error

---

## Authentication Endpoints

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "username": "johndo<PERSON>",
  "role": "content_creator" // or "content_booster"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "username": "johndoe",
      "role": "content_creator",
      "status": "pending_verification"
    },
    "access_token": "jwt_token",
    "refresh_token": "refresh_token"
  }
}
```

### POST /auth/login
Authenticate user and get access tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword"
}
```

### POST /auth/refresh
Refresh access token using refresh token.

### POST /auth/logout
Logout user and invalidate tokens.

### POST /auth/forgot-password
Request password reset email.

### POST /auth/reset-password
Reset password using reset token.

---

## User Management Endpoints

### GET /users/profile
Get current user profile.

### PUT /users/profile
Update current user profile.

### POST /users/upload-avatar
Upload user avatar image.

### GET /users/{user_id}
Get public user profile.

### POST /users/verify-email
Verify email address with verification token.

---

## Social Accounts Endpoints

### GET /social-accounts
Get user's connected social media accounts.

### POST /social-accounts
Connect a new social media account.

**Request Body:**
```json
{
  "platform": "youtube",
  "account_url": "https://youtube.com/@username",
  "account_username": "username"
}
```

### PUT /social-accounts/{account_id}
Update social media account information.

### DELETE /social-accounts/{account_id}
Disconnect social media account.

### POST /social-accounts/{account_id}/verify
Verify ownership of social media account.

---

## Campaign Management Endpoints

### GET /campaigns
Get campaigns with filtering and pagination.

**Query Parameters:**
- `status`: Filter by campaign status
- `platform`: Filter by social media platform
- `creator_id`: Filter by creator
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)

### POST /campaigns
Create a new campaign (Content Creators only).

**Request Body:**
```json
{
  "social_account_id": "uuid",
  "title": "Get 1000 YouTube Likes",
  "description": "Help boost my latest video",
  "platform": "youtube",
  "engagement_type": "likes",
  "target_url": "https://youtube.com/watch?v=xyz",
  "target_quantity": 1000,
  "price_per_task": 0.50,
  "end_date": "2024-12-31T23:59:59Z",
  "max_tasks_per_user": 5,
  "instructions": "Please like the video and leave it liked for at least 24 hours"
}
```

### GET /campaigns/{campaign_id}
Get campaign details.

### PUT /campaigns/{campaign_id}
Update campaign (Creator only).

### DELETE /campaigns/{campaign_id}
Delete campaign (Creator only).

### POST /campaigns/{campaign_id}/pause
Pause active campaign.

### POST /campaigns/{campaign_id}/resume
Resume paused campaign.

---

## Task Management Endpoints

### GET /tasks
Get tasks for current user (boosters see available tasks, creators see their campaign tasks).

### POST /tasks/{task_id}/claim
Claim an available task (Content Boosters only).

### POST /tasks/{task_id}/submit
Submit task completion with proof.

**Request Body:**
```json
{
  "proof_url": "https://screenshot-url.com/proof.png",
  "proof_description": "Screenshot showing the like was added",
  "completion_notes": "Task completed successfully"
}
```

### POST /tasks/{task_id}/approve
Approve completed task (Content Creators only).

### POST /tasks/{task_id}/reject
Reject completed task with reason.

**Request Body:**
```json
{
  "rejection_reason": "Proof is not clear enough"
}
```

---

## Payment Endpoints

### GET /payments/balance
Get current user's balance and earnings.

### POST /payments/deposit
Add funds to account (Content Creators).

### POST /payments/withdraw
Withdraw earnings (Content Boosters).

### GET /payments/transactions
Get payment transaction history.

### POST /payments/setup-intent
Create Stripe setup intent for payment method.

---

## Admin Endpoints

### GET /admin/users
Get all users with filtering (Admin only).

### PUT /admin/users/{user_id}/status
Update user status (Admin only).

### GET /admin/campaigns
Get all campaigns (Admin only).

### GET /admin/transactions
Get all transactions (Admin only).

### GET /admin/analytics
Get platform analytics (Admin only).

---

## Notification Endpoints

### GET /notifications
Get user notifications.

### PUT /notifications/{notification_id}/read
Mark notification as read.

### PUT /notifications/mark-all-read
Mark all notifications as read.

---

## Rate Limiting
- Authentication endpoints: 5 requests per minute
- General API endpoints: 100 requests per minute
- File upload endpoints: 10 requests per minute

## Webhooks
The platform supports webhooks for real-time updates:

### Stripe Webhooks
- `payment_intent.succeeded`
- `payment_intent.payment_failed`
- `transfer.created`

### Platform Events
- `campaign.completed`
- `task.submitted`
- `task.approved`
- `user.verified`
