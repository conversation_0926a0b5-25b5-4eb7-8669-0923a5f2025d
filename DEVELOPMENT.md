# Development Guide - Social Media Engagement Platform

## 🛠️ Development Setup

This guide covers setting up the development environment for the Social Media Engagement Platform.

## Prerequisites

- **Node.js** 18+ and npm
- **Python** 3.11+
- **Docker** and Docker Compose
- **Git**

## Quick Start

### 1. Clone Repository
```bash
git clone <repository-url>
cd social-media-engagement-platform
```

### 2. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

### 3. Start Development Environment
```bash
# Start all services with Docker Compose
docker-compose --profile development up -d

# Or start services individually (see below)
```

## Backend Development

### Setup
```bash
cd backend

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Set up database
alembic upgrade head

# Run development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/test_auth.py

# Run specific test
pytest tests/test_auth.py::TestAuth::test_login_success

# Run tests with verbose output
pytest -v

# Run tests and stop on first failure
pytest -x
```

### Database Management
```bash
# Create new migration
alembic revision --autogenerate -m "Description of changes"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1

# Reset database (development only)
alembic downgrade base
alembic upgrade head
```

### Code Quality
```bash
# Format code
black app/ tests/

# Sort imports
isort app/ tests/

# Lint code
flake8 app/ tests/

# Type checking
mypy app/
```

## Frontend Development

### Setup
```bash
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### Code Quality
```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint --fix

# Type checking
npm run type-check

# Format code (if Prettier is configured)
npm run format
```

### Testing (Frontend)
```bash
# Run tests (when implemented)
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## Docker Development

### Full Stack with Docker
```bash
# Start all services
docker-compose --profile development up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Rebuild services
docker-compose build
docker-compose up -d
```

### Individual Services
```bash
# Start only database and Redis
docker-compose up -d postgres redis

# Start backend only
docker-compose up -d backend

# Start frontend only
docker-compose up -d frontend
```

### Database Access
```bash
# Access PostgreSQL
docker-compose exec postgres psql -U postgres -d social_engagement

# Access Redis
docker-compose exec redis redis-cli

# View database with Adminer
# Visit http://localhost:8080
```

## API Development

### Testing API Endpoints
```bash
# Health check
curl http://localhost:8000/health

# Register user
curl -X POST http://localhost:8000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPass123",
    "first_name": "Test",
    "last_name": "User",
    "username": "testuser",
    "role": "content_booster"
  }'

# Login
curl -X POST http://localhost:8000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "TestPass123"
  }'

# Access protected endpoint
curl -X GET http://localhost:8000/api/v1/users/profile \
  -H "Authorization: Bearer YOUR_TOKEN_HERE"
```

### API Documentation
- **Swagger UI**: http://localhost:8000/api/v1/docs
- **ReDoc**: http://localhost:8000/api/v1/redoc

## Development Tools

### Database Tools
- **Adminer**: http://localhost:8080 (when running with development profile)
- **pgAdmin**: Can be added to docker-compose for PostgreSQL management

### Email Testing
- **Mailhog**: http://localhost:8025 (when running with development profile)
- Captures all outgoing emails for testing

### File Storage
- **MinIO**: http://localhost:9001 (S3-compatible storage for development)
- Username: `minioadmin`, Password: `minioadmin123`

## Environment Variables

### Required Variables
```bash
# Database
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/social_engagement

# Redis
REDIS_URL=redis://localhost:6379

# JWT
JWT_SECRET_KEY=your-secret-key-min-32-chars

# Stripe (use test keys)
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...

# Email (use Mailhog for development)
EMAIL_HOST=localhost
EMAIL_PORT=1025
EMAIL_USER=
EMAIL_PASSWORD=
```

### Optional Variables
```bash
# OAuth (for testing OAuth features)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Monitoring
SENTRY_DSN=your-sentry-dsn

# File Upload
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
```

## Debugging

### Backend Debugging
```bash
# Run with debugger
python -m debugpy --listen 0.0.0.0:5678 --wait-for-client -m uvicorn app.main:app --reload

# View logs
docker-compose logs -f backend

# Access container
docker-compose exec backend bash
```

### Frontend Debugging
```bash
# Run with debugging
npm run dev

# View logs
docker-compose logs -f frontend

# Access container
docker-compose exec frontend sh
```

### Database Debugging
```bash
# View database logs
docker-compose logs -f postgres

# Check database connections
docker-compose exec postgres pg_isready -U postgres

# View active connections
docker-compose exec postgres psql -U postgres -c "SELECT * FROM pg_stat_activity;"
```

## Common Development Tasks

### Adding New API Endpoint
1. Create endpoint in `backend/app/api/v1/endpoints/`
2. Add to router in `backend/app/api/v1/api.py`
3. Create Pydantic schemas in `backend/app/schemas/`
4. Add business logic to `backend/app/services/`
5. Write tests in `backend/tests/`

### Adding New Database Model
1. Create model in `backend/app/models/`
2. Import in `backend/app/core/database.py`
3. Generate migration: `alembic revision --autogenerate -m "Add new model"`
4. Apply migration: `alembic upgrade head`

### Adding New Frontend Page
1. Create page component in `frontend/src/pages/`
2. Add routing if needed
3. Create reusable components in `frontend/src/components/`
4. Add API calls in `frontend/src/services/`

## Performance Optimization

### Backend
- Use database indexes for frequently queried fields
- Implement caching with Redis
- Use async/await for I/O operations
- Optimize database queries (avoid N+1 problems)

### Frontend
- Use React.memo for expensive components
- Implement lazy loading for routes
- Optimize images and assets
- Use React Query for API state management

### Database
- Add indexes for foreign keys and search fields
- Use connection pooling
- Monitor slow queries
- Regular VACUUM and ANALYZE

## Security Best Practices

### Development
- Never commit secrets to version control
- Use environment variables for configuration
- Validate all inputs
- Use HTTPS in production
- Implement rate limiting
- Regular dependency updates

### Testing
- Test authentication and authorization
- Test input validation
- Test error handling
- Test edge cases
- Mock external services

## Troubleshooting

### Common Issues

**Port Already in Use**:
```bash
# Find process using port
lsof -i :8000
# Kill process
kill -9 <PID>
```

**Database Connection Issues**:
```bash
# Check if PostgreSQL is running
docker-compose ps postgres
# Restart PostgreSQL
docker-compose restart postgres
```

**Permission Issues**:
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
```

**Module Not Found**:
```bash
# Reinstall dependencies
pip install -r requirements.txt
# or
npm install
```

### Getting Help
- Check logs: `docker-compose logs -f [service]`
- Verify environment variables
- Check service health: `docker-compose ps`
- Review API documentation
- Check database connectivity

## Contributing

### Code Style
- Follow PEP 8 for Python
- Use TypeScript for frontend
- Write descriptive commit messages
- Add tests for new features
- Update documentation

### Pull Request Process
1. Create feature branch
2. Write tests
3. Ensure all tests pass
4. Update documentation
5. Submit pull request
6. Address review feedback

### Release Process
1. Update version numbers
2. Update CHANGELOG.md
3. Create release tag
4. Deploy to staging
5. Run integration tests
6. Deploy to production

This development guide provides everything needed to set up and work on the Social Media Engagement Platform efficiently.
