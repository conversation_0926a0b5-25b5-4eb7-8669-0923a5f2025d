import pytest
import asyncio
from typing import AsyncGenerator
from httpx import As<PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import get_db, Base
from app.core.config import settings
from app.models.user import User, UserRole, UserStatus
from app.core.security import get_password_hash

# Test database URL
TEST_DATABASE_URL = "sqlite+aiosqlite:///./test.db"

# Create test engine
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

TestingSessionLocal = async_sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False,
)


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a test database session."""
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    async with TestingSessionLocal() as session:
        yield session
    
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest.fixture
async def client(db_session: AsyncSession) -> AsyncGenerator[AsyncClient, None]:
    """Create a test client."""
    def get_test_db():
        return db_session
    
    app.dependency_overrides[get_db] = get_test_db
    
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac
    
    app.dependency_overrides.clear()


@pytest.fixture
async def test_user(db_session: AsyncSession) -> User:
    """Create a test user."""
    user = User(
        email="<EMAIL>",
        password_hash=get_password_hash("testpassword"),
        first_name="Test",
        last_name="User",
        username="testuser",
        role=UserRole.CONTENT_BOOSTER,
        status=UserStatus.ACTIVE,
        email_verified=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
async def test_creator(db_session: AsyncSession) -> User:
    """Create a test content creator."""
    user = User(
        email="<EMAIL>",
        password_hash=get_password_hash("creatorpassword"),
        first_name="Content",
        last_name="Creator",
        username="contentcreator",
        role=UserRole.CONTENT_CREATOR,
        status=UserStatus.ACTIVE,
        email_verified=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
async def test_admin(db_session: AsyncSession) -> User:
    """Create a test admin user."""
    user = User(
        email="<EMAIL>",
        password_hash=get_password_hash("adminpassword"),
        first_name="Admin",
        last_name="User",
        username="adminuser",
        role=UserRole.ADMIN,
        status=UserStatus.ACTIVE,
        email_verified=True
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user


@pytest.fixture
async def auth_headers(client: AsyncClient, test_user: User) -> dict:
    """Get authentication headers for test user."""
    response = await client.post("/api/v1/auth/login", json={
        "email": test_user.email,
        "password": "testpassword"
    })
    assert response.status_code == 200
    data = response.json()
    token = data["data"]["tokens"]["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def creator_auth_headers(client: AsyncClient, test_creator: User) -> dict:
    """Get authentication headers for test creator."""
    response = await client.post("/api/v1/auth/login", json={
        "email": test_creator.email,
        "password": "creatorpassword"
    })
    assert response.status_code == 200
    data = response.json()
    token = data["data"]["tokens"]["access_token"]
    return {"Authorization": f"Bearer {token}"}


@pytest.fixture
async def admin_auth_headers(client: AsyncClient, test_admin: User) -> dict:
    """Get authentication headers for test admin."""
    response = await client.post("/api/v1/auth/login", json={
        "email": test_admin.email,
        "password": "adminpassword"
    })
    assert response.status_code == 200
    data = response.json()
    token = data["data"]["tokens"]["access_token"]
    return {"Authorization": f"Bearer {token}"}
