import pytest
from httpx import AsyncClient
from app.models.user import User


class TestAuth:
    """Test authentication endpoints."""

    async def test_register_content_creator(self, client: AsyncClient):
        """Test user registration for content creator."""
        response = await client.post("/api/v1/auth/register", json={
            "email": "<EMAIL>",
            "password": "SecurePass123",
            "first_name": "New",
            "last_name": "C<PERSON>",
            "username": "newcreator",
            "role": "content_creator"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["user"]["email"] == "<EMAIL>"
        assert data["data"]["user"]["role"] == "content_creator"
        assert "access_token" in data["data"]["tokens"]
        assert "refresh_token" in data["data"]["tokens"]

    async def test_register_content_booster(self, client: AsyncClient):
        """Test user registration for content booster."""
        response = await client.post("/api/v1/auth/register", json={
            "email": "<EMAIL>",
            "password": "SecurePass123",
            "first_name": "New",
            "last_name": "Booster",
            "username": "newbooster",
            "role": "content_booster"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["user"]["role"] == "content_booster"

    async def test_register_duplicate_email(self, client: AsyncClient, test_user: User):
        """Test registration with duplicate email."""
        response = await client.post("/api/v1/auth/register", json={
            "email": test_user.email,
            "password": "SecurePass123",
            "first_name": "Duplicate",
            "last_name": "User",
            "username": "duplicateuser",
            "role": "content_booster"
        })
        
        assert response.status_code == 400

    async def test_register_duplicate_username(self, client: AsyncClient, test_user: User):
        """Test registration with duplicate username."""
        response = await client.post("/api/v1/auth/register", json={
            "email": "<EMAIL>",
            "password": "SecurePass123",
            "first_name": "Different",
            "last_name": "User",
            "username": test_user.username,
            "role": "content_booster"
        })
        
        assert response.status_code == 400

    async def test_register_weak_password(self, client: AsyncClient):
        """Test registration with weak password."""
        response = await client.post("/api/v1/auth/register", json={
            "email": "<EMAIL>",
            "password": "weak",
            "first_name": "Weak",
            "last_name": "Password",
            "username": "weakpass",
            "role": "content_booster"
        })
        
        assert response.status_code == 422

    async def test_login_success(self, client: AsyncClient, test_user: User):
        """Test successful login."""
        response = await client.post("/api/v1/auth/login", json={
            "email": test_user.email,
            "password": "testpassword"
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["user"]["email"] == test_user.email
        assert "access_token" in data["data"]["tokens"]
        assert "refresh_token" in data["data"]["tokens"]

    async def test_login_invalid_email(self, client: AsyncClient):
        """Test login with invalid email."""
        response = await client.post("/api/v1/auth/login", json={
            "email": "<EMAIL>",
            "password": "testpassword"
        })
        
        assert response.status_code == 401

    async def test_login_invalid_password(self, client: AsyncClient, test_user: User):
        """Test login with invalid password."""
        response = await client.post("/api/v1/auth/login", json={
            "email": test_user.email,
            "password": "wrongpassword"
        })
        
        assert response.status_code == 401

    async def test_refresh_token(self, client: AsyncClient, test_user: User):
        """Test token refresh."""
        # First login to get tokens
        login_response = await client.post("/api/v1/auth/login", json={
            "email": test_user.email,
            "password": "testpassword"
        })
        
        assert login_response.status_code == 200
        login_data = login_response.json()
        refresh_token = login_data["data"]["tokens"]["refresh_token"]
        
        # Test refresh
        refresh_response = await client.post("/api/v1/auth/refresh", json={
            "refresh_token": refresh_token
        })
        
        assert refresh_response.status_code == 200
        refresh_data = refresh_response.json()
        assert "access_token" in refresh_data["data"]

    async def test_refresh_invalid_token(self, client: AsyncClient):
        """Test refresh with invalid token."""
        response = await client.post("/api/v1/auth/refresh", json={
            "refresh_token": "invalid_token"
        })
        
        assert response.status_code == 401

    async def test_logout(self, client: AsyncClient):
        """Test logout."""
        response = await client.post("/api/v1/auth/logout")
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    async def test_protected_endpoint_without_token(self, client: AsyncClient):
        """Test accessing protected endpoint without token."""
        response = await client.get("/api/v1/users/profile")
        
        assert response.status_code == 403

    async def test_protected_endpoint_with_token(self, client: AsyncClient, auth_headers: dict):
        """Test accessing protected endpoint with valid token."""
        response = await client.get("/api/v1/users/profile", headers=auth_headers)
        
        # This would return 200 if the endpoint was fully implemented
        # For now, we expect it to not return 403 (unauthorized)
        assert response.status_code != 403
