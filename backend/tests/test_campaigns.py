import pytest
from httpx import AsyncClient
from app.models.user import User
from app.models.campaign import Campaign
from app.models.user import SocialAccount


class TestCampaigns:
    """Test campaign endpoints."""

    @pytest.fixture
    async def test_social_account(self, db_session, test_creator: User):
        """Create a test social account."""
        social_account = SocialAccount(
            user_id=test_creator.id,
            platform="youtube",
            account_url="https://youtube.com/@testcreator",
            account_username="testcreator",
            verified=True
        )
        db_session.add(social_account)
        await db_session.commit()
        await db_session.refresh(social_account)
        return social_account

    async def test_get_campaigns_as_creator(self, client: AsyncClient, creator_auth_headers: dict):
        """Test getting campaigns as content creator."""
        response = await client.get("/api/v1/campaigns/", headers=creator_auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "campaigns" in data["data"]
        assert "pagination" in data["data"]

    async def test_get_campaigns_as_booster(self, client: AsyncClient, auth_headers: dict):
        """Test getting campaigns as content booster (should see active campaigns only)."""
        response = await client.get("/api/v1/campaigns/", headers=auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "campaigns" in data["data"]

    async def test_create_campaign_as_creator(self, client: AsyncClient, creator_auth_headers: dict, test_social_account):
        """Test creating campaign as content creator."""
        campaign_data = {
            "social_account_id": str(test_social_account.id),
            "title": "Test Campaign",
            "description": "A test campaign for YouTube likes",
            "platform": "youtube",
            "engagement_type": "likes",
            "target_url": "https://youtube.com/watch?v=test123",
            "target_quantity": 100,
            "price_per_task": 0.50,
            "max_tasks_per_user": 5,
            "min_booster_rating": 0.0,
            "instructions": "Please like the video",
            "proof_required": True,
            "auto_approve": False
        }
        
        response = await client.post("/api/v1/campaigns/", json=campaign_data, headers=creator_auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["title"] == "Test Campaign"
        assert data["data"]["status"] == "draft"

    async def test_create_campaign_as_booster(self, client: AsyncClient, auth_headers: dict, test_social_account):
        """Test creating campaign as content booster (should fail)."""
        campaign_data = {
            "social_account_id": str(test_social_account.id),
            "title": "Test Campaign",
            "description": "A test campaign",
            "platform": "youtube",
            "engagement_type": "likes",
            "target_url": "https://youtube.com/watch?v=test123",
            "target_quantity": 100,
            "price_per_task": 0.50
        }
        
        response = await client.post("/api/v1/campaigns/", json=campaign_data, headers=auth_headers)
        
        assert response.status_code == 403

    async def test_create_campaign_invalid_data(self, client: AsyncClient, creator_auth_headers: dict):
        """Test creating campaign with invalid data."""
        campaign_data = {
            "title": "",  # Empty title
            "target_quantity": -1,  # Negative quantity
            "price_per_task": 0  # Zero price
        }
        
        response = await client.post("/api/v1/campaigns/", json=campaign_data, headers=creator_auth_headers)
        
        assert response.status_code == 422

    async def test_get_campaign_details(self, client: AsyncClient, creator_auth_headers: dict, test_social_account):
        """Test getting campaign details."""
        # First create a campaign
        campaign_data = {
            "social_account_id": str(test_social_account.id),
            "title": "Detail Test Campaign",
            "platform": "youtube",
            "engagement_type": "likes",
            "target_url": "https://youtube.com/watch?v=detail123",
            "target_quantity": 50,
            "price_per_task": 0.25
        }
        
        create_response = await client.post("/api/v1/campaigns/", json=campaign_data, headers=creator_auth_headers)
        assert create_response.status_code == 200
        campaign_id = create_response.json()["data"]["id"]
        
        # Get campaign details
        response = await client.get(f"/api/v1/campaigns/{campaign_id}", headers=creator_auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["title"] == "Detail Test Campaign"

    async def test_get_nonexistent_campaign(self, client: AsyncClient, creator_auth_headers: dict):
        """Test getting details of nonexistent campaign."""
        fake_id = "********-0000-0000-0000-************"
        response = await client.get(f"/api/v1/campaigns/{fake_id}", headers=creator_auth_headers)
        
        assert response.status_code == 404

    async def test_update_campaign(self, client: AsyncClient, creator_auth_headers: dict, test_social_account):
        """Test updating campaign."""
        # First create a campaign
        campaign_data = {
            "social_account_id": str(test_social_account.id),
            "title": "Update Test Campaign",
            "platform": "youtube",
            "engagement_type": "likes",
            "target_url": "https://youtube.com/watch?v=update123",
            "target_quantity": 75,
            "price_per_task": 0.30
        }
        
        create_response = await client.post("/api/v1/campaigns/", json=campaign_data, headers=creator_auth_headers)
        assert create_response.status_code == 200
        campaign_id = create_response.json()["data"]["id"]
        
        # Update campaign
        update_data = {
            "title": "Updated Campaign Title",
            "target_quantity": 100
        }
        
        response = await client.put(f"/api/v1/campaigns/{campaign_id}", json=update_data, headers=creator_auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True

    async def test_update_campaign_unauthorized(self, client: AsyncClient, auth_headers: dict):
        """Test updating campaign as unauthorized user."""
        fake_id = "********-0000-0000-0000-************"
        update_data = {"title": "Unauthorized Update"}
        
        response = await client.put(f"/api/v1/campaigns/{fake_id}", json=update_data, headers=auth_headers)
        
        assert response.status_code == 403

    async def test_delete_campaign(self, client: AsyncClient, creator_auth_headers: dict, test_social_account):
        """Test deleting campaign."""
        # First create a campaign
        campaign_data = {
            "social_account_id": str(test_social_account.id),
            "title": "Delete Test Campaign",
            "platform": "youtube",
            "engagement_type": "likes",
            "target_url": "https://youtube.com/watch?v=delete123",
            "target_quantity": 25,
            "price_per_task": 0.20
        }
        
        create_response = await client.post("/api/v1/campaigns/", json=campaign_data, headers=creator_auth_headers)
        assert create_response.status_code == 200
        campaign_id = create_response.json()["data"]["id"]
        
        # Delete campaign
        response = await client.delete(f"/api/v1/campaigns/{campaign_id}", headers=creator_auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["deleted"] is True

    async def test_campaign_pagination(self, client: AsyncClient, creator_auth_headers: dict):
        """Test campaign pagination."""
        response = await client.get("/api/v1/campaigns/?page=1&limit=5", headers=creator_auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["pagination"]["page"] == 1
        assert data["data"]["pagination"]["limit"] == 5

    async def test_campaign_filtering(self, client: AsyncClient, creator_auth_headers: dict):
        """Test campaign filtering by status and platform."""
        response = await client.get("/api/v1/campaigns/?status=draft&platform=youtube", headers=creator_auth_headers)
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
