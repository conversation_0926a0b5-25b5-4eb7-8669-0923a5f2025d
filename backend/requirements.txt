# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
asyncpg==0.29.0
sqlalchemy==2.0.23
alembic==1.12.1

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-decouple==3.8

# HTTP client
httpx==0.25.2
aiofiles==23.2.1

# Validation
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# Redis
redis==5.0.1
aioredis==2.0.1

# Payment processing
stripe==7.8.0

# Email
fastapi-mail==1.4.1

# OAuth
authlib==1.2.1

# Image processing
Pillow==10.1.0

# Background tasks
celery==5.3.4

# Monitoring and logging
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
pytest-mock==3.12.0

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# CORS
python-cors==1.0.0

# File upload
python-magic==0.4.27

# Rate limiting
slowapi==0.1.9

# UUID
uuid==1.30
