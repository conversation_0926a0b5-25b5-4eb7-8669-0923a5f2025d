from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.exc import IntegrityError
from app.models.user import User, SocialAccount
from app.schemas.user import UserCreate, UserUpdate
from app.core.security import get_password_hash
from datetime import datetime
from typing import Optional
import uuid


class UserService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_user(self, user_data: UserCreate) -> User:
        """Create a new user"""
        try:
            # Hash password
            hashed_password = get_password_hash(user_data.password)
            
            # Create user instance
            user = User(
                email=user_data.email,
                password_hash=hashed_password,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
                username=user_data.username,
                role=user_data.role,
                bio=user_data.bio,
                phone=user_data.phone,
                country=user_data.country,
                timezone=user_data.timezone
            )
            
            self.db.add(user)
            await self.db.commit()
            await self.db.refresh(user)
            
            return user
            
        except IntegrityError as e:
            await self.db.rollback()
            if "email" in str(e):
                raise ValueError("Email already exists")
            elif "username" in str(e):
                raise ValueError("Username already exists")
            else:
                raise ValueError("User creation failed")

    async def get_user_by_id(self, user_id: uuid.UUID) -> Optional[User]:
        """Get user by ID"""
        result = await self.db.execute(
            select(User).where(User.id == user_id)
        )
        return result.scalar_one_or_none()

    async def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        result = await self.db.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()

    async def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        result = await self.db.execute(
            select(User).where(User.username == username)
        )
        return result.scalar_one_or_none()

    async def update_user(self, user_id: uuid.UUID, user_data: UserUpdate) -> Optional[User]:
        """Update user information"""
        try:
            # Get existing user
            user = await self.get_user_by_id(user_id)
            if not user:
                return None
            
            # Update fields
            update_data = user_data.dict(exclude_unset=True)
            if update_data:
                await self.db.execute(
                    update(User)
                    .where(User.id == user_id)
                    .values(**update_data, updated_at=datetime.utcnow())
                )
                await self.db.commit()
                
                # Refresh user
                await self.db.refresh(user)
            
            return user
            
        except IntegrityError:
            await self.db.rollback()
            raise ValueError("Update failed - duplicate data")

    async def update_last_login(self, user_id: uuid.UUID) -> None:
        """Update user's last login timestamp"""
        await self.db.execute(
            update(User)
            .where(User.id == user_id)
            .values(last_login=datetime.utcnow())
        )
        await self.db.commit()

    async def verify_email(self, user_id: uuid.UUID) -> bool:
        """Mark user's email as verified"""
        try:
            await self.db.execute(
                update(User)
                .where(User.id == user_id)
                .values(email_verified=True, updated_at=datetime.utcnow())
            )
            await self.db.commit()
            return True
        except Exception:
            await self.db.rollback()
            return False

    async def update_user_status(self, user_id: uuid.UUID, status: str) -> bool:
        """Update user status (admin only)"""
        try:
            await self.db.execute(
                update(User)
                .where(User.id == user_id)
                .values(status=status, updated_at=datetime.utcnow())
            )
            await self.db.commit()
            return True
        except Exception:
            await self.db.rollback()
            return False

    async def get_user_social_accounts(self, user_id: uuid.UUID) -> list[SocialAccount]:
        """Get user's social media accounts"""
        result = await self.db.execute(
            select(SocialAccount).where(SocialAccount.user_id == user_id)
        )
        return result.scalars().all()

    async def create_social_account(self, user_id: uuid.UUID, account_data: dict) -> SocialAccount:
        """Create a new social media account for user"""
        try:
            social_account = SocialAccount(
                user_id=user_id,
                **account_data
            )
            
            self.db.add(social_account)
            await self.db.commit()
            await self.db.refresh(social_account)
            
            return social_account
            
        except IntegrityError:
            await self.db.rollback()
            raise ValueError("Social account already exists")

    async def delete_user(self, user_id: uuid.UUID) -> bool:
        """Delete user (soft delete by updating status)"""
        try:
            await self.db.execute(
                update(User)
                .where(User.id == user_id)
                .values(status="banned", updated_at=datetime.utcnow())
            )
            await self.db.commit()
            return True
        except Exception:
            await self.db.rollback()
            return False
