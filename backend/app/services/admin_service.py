from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func, and_, or_, desc
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
import uuid

from app.models.user import User, UserRole, UserStatus
from app.models.campaign import Campaign, CampaignStatus
from app.models.task import Task, TaskStatus
from app.models.transaction import Transaction, TransactionStatus
from app.models.audit_log import AuditLog
import logging

logger = logging.getLogger(__name__)


class AdminService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_platform_analytics(self) -> Dict[str, Any]:
        """Get comprehensive platform analytics"""
        try:
            # User statistics
            user_stats_result = await self.db.execute(
                select(
                    User.role,
                    User.status,
                    func.count(User.id).label('count')
                ).group_by(User.role, User.status)
            )
            user_stats = {}
            for row in user_stats_result:
                if row.role not in user_stats:
                    user_stats[row.role] = {}
                user_stats[row.role][row.status] = row.count

            # Campaign statistics
            campaign_stats_result = await self.db.execute(
                select(
                    Campaign.status,
                    func.count(Campaign.id).label('count'),
                    func.sum(Campaign.total_budget).label('total_budget')
                ).group_by(Campaign.status)
            )
            campaign_stats = {}
            for row in campaign_stats_result:
                campaign_stats[row.status] = {
                    'count': row.count,
                    'total_budget': float(row.total_budget) if row.total_budget else 0
                }

            # Task statistics
            task_stats_result = await self.db.execute(
                select(
                    Task.status,
                    func.count(Task.id).label('count')
                ).group_by(Task.status)
            )
            task_stats = {row.status: row.count for row in task_stats_result}

            # Transaction statistics
            transaction_stats_result = await self.db.execute(
                select(
                    Transaction.status,
                    func.count(Transaction.id).label('count'),
                    func.sum(Transaction.amount).label('total_amount')
                ).group_by(Transaction.status)
            )
            transaction_stats = {}
            for row in transaction_stats_result:
                transaction_stats[row.status] = {
                    'count': row.count,
                    'total_amount': float(row.total_amount) if row.total_amount else 0
                }

            # Revenue statistics
            platform_revenue_result = await self.db.execute(
                select(func.sum(Transaction.fee)).where(
                    Transaction.status == TransactionStatus.COMPLETED
                )
            )
            platform_revenue = platform_revenue_result.scalar() or 0

            # Growth metrics (last 30 days)
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            
            new_users_result = await self.db.execute(
                select(func.count(User.id)).where(User.created_at >= thirty_days_ago)
            )
            new_users = new_users_result.scalar() or 0

            new_campaigns_result = await self.db.execute(
                select(func.count(Campaign.id)).where(Campaign.created_at >= thirty_days_ago)
            )
            new_campaigns = new_campaigns_result.scalar() or 0

            # Daily activity (last 7 days)
            seven_days_ago = datetime.utcnow() - timedelta(days=7)
            daily_activity_result = await self.db.execute(
                select(
                    func.date(Task.completed_at).label('date'),
                    func.count(Task.id).label('completed_tasks')
                ).where(
                    and_(
                        Task.status == TaskStatus.COMPLETED,
                        Task.completed_at >= seven_days_ago
                    )
                ).group_by(func.date(Task.completed_at)).order_by(func.date(Task.completed_at))
            )
            daily_activity = [
                {'date': row.date.isoformat(), 'completed_tasks': row.completed_tasks}
                for row in daily_activity_result
            ]

            return {
                'user_statistics': user_stats,
                'campaign_statistics': campaign_stats,
                'task_statistics': task_stats,
                'transaction_statistics': transaction_stats,
                'platform_revenue': float(platform_revenue),
                'growth_metrics': {
                    'new_users_30_days': new_users,
                    'new_campaigns_30_days': new_campaigns
                },
                'daily_activity': daily_activity,
                'generated_at': datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get platform analytics: {e}")
            raise

    async def get_users(
        self,
        role: Optional[UserRole] = None,
        status: Optional[UserStatus] = None,
        search: Optional[str] = None,
        page: int = 1,
        limit: int = 20
    ) -> Dict[str, Any]:
        """Get users with filtering and pagination"""
        try:
            offset = (page - 1) * limit
            
            # Build query
            query = select(User)
            conditions = []
            
            if role:
                conditions.append(User.role == role)
            if status:
                conditions.append(User.status == status)
            if search:
                search_term = f"%{search}%"
                conditions.append(
                    or_(
                        User.email.ilike(search_term),
                        User.username.ilike(search_term),
                        User.first_name.ilike(search_term),
                        User.last_name.ilike(search_term)
                    )
                )
            
            if conditions:
                query = query.where(and_(*conditions))
            
            # Get users with pagination
            result = await self.db.execute(
                query.order_by(User.created_at.desc()).offset(offset).limit(limit)
            )
            users = result.scalars().all()
            
            # Get total count
            count_query = select(func.count(User.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            count_result = await self.db.execute(count_query)
            total = count_result.scalar()
            
            # Format response
            users_data = []
            for user in users:
                users_data.append({
                    'id': str(user.id),
                    'email': user.email,
                    'username': user.username,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'role': user.role,
                    'status': user.status,
                    'email_verified': user.email_verified,
                    'rating': float(user.rating),
                    'total_earnings': float(user.total_earnings),
                    'total_spent': float(user.total_spent),
                    'last_login': user.last_login.isoformat() if user.last_login else None,
                    'created_at': user.created_at.isoformat(),
                    'updated_at': user.updated_at.isoformat()
                })
            
            return {
                'users': users_data,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total,
                    'has_next': (page * limit) < total,
                    'has_prev': page > 1,
                    'total_pages': (total + limit - 1) // limit
                }
            }

        except Exception as e:
            logger.error(f"Failed to get users: {e}")
            raise

    async def update_user_status(self, user_id: uuid.UUID, new_status: UserStatus, admin_id: uuid.UUID) -> bool:
        """Update user status (suspend, ban, activate)"""
        try:
            # Get current user
            result = await self.db.execute(select(User).where(User.id == user_id))
            user = result.scalar_one_or_none()
            
            if not user:
                raise ValueError("User not found")
            
            old_status = user.status
            
            # Update user status
            await self.db.execute(
                update(User).where(User.id == user_id).values(
                    status=new_status,
                    updated_at=datetime.utcnow()
                )
            )
            
            # Log the action
            audit_log = AuditLog(
                user_id=admin_id,
                action=f"update_user_status",
                resource_type="user",
                resource_id=user_id,
                old_values={"status": old_status},
                new_values={"status": new_status}
            )
            self.db.add(audit_log)
            
            await self.db.commit()
            return True

        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update user status: {e}")
            raise

    async def get_suspicious_activities(self, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """Get suspicious activities for fraud detection"""
        try:
            offset = (page - 1) * limit
            
            # Define suspicious patterns
            suspicious_activities = []
            
            # 1. Users with multiple failed login attempts (simulated - would need login attempt tracking)
            # 2. Users completing tasks too quickly
            quick_completions_result = await self.db.execute(
                select(
                    User.id,
                    User.username,
                    func.count(Task.id).label('quick_tasks'),
                    func.avg(
                        func.extract('epoch', Task.completed_at - Task.assigned_at) / 60
                    ).label('avg_completion_minutes')
                ).join(Task, Task.booster_id == User.id)
                .where(
                    and_(
                        Task.status == TaskStatus.COMPLETED,
                        Task.assigned_at.isnot(None),
                        Task.completed_at.isnot(None),
                        func.extract('epoch', Task.completed_at - Task.assigned_at) < 300  # Less than 5 minutes
                    )
                ).group_by(User.id, User.username)
                .having(func.count(Task.id) > 5)  # More than 5 quick completions
                .order_by(func.count(Task.id).desc())
                .offset(offset).limit(limit)
            )
            
            for row in quick_completions_result:
                suspicious_activities.append({
                    'type': 'quick_task_completion',
                    'user_id': str(row.id),
                    'username': row.username,
                    'details': {
                        'quick_tasks_count': row.quick_tasks,
                        'avg_completion_minutes': float(row.avg_completion_minutes) if row.avg_completion_minutes else 0
                    },
                    'severity': 'high' if row.quick_tasks > 10 else 'medium',
                    'detected_at': datetime.utcnow().isoformat()
                })
            
            # 3. Users with high rejection rates
            high_rejection_result = await self.db.execute(
                select(
                    User.id,
                    User.username,
                    func.count(Task.id).label('total_tasks'),
                    func.sum(func.case((Task.status == TaskStatus.REJECTED, 1), else_=0)).label('rejected_tasks')
                ).join(Task, Task.booster_id == User.id)
                .group_by(User.id, User.username)
                .having(
                    and_(
                        func.count(Task.id) > 10,  # At least 10 tasks
                        func.sum(func.case((Task.status == TaskStatus.REJECTED, 1), else_=0)) / func.count(Task.id) > 0.3  # More than 30% rejection rate
                    )
                ).order_by((func.sum(func.case((Task.status == TaskStatus.REJECTED, 1), else_=0)) / func.count(Task.id)).desc())
            )
            
            for row in high_rejection_result:
                rejection_rate = row.rejected_tasks / row.total_tasks if row.total_tasks > 0 else 0
                suspicious_activities.append({
                    'type': 'high_rejection_rate',
                    'user_id': str(row.id),
                    'username': row.username,
                    'details': {
                        'total_tasks': row.total_tasks,
                        'rejected_tasks': row.rejected_tasks,
                        'rejection_rate': round(rejection_rate * 100, 2)
                    },
                    'severity': 'high' if rejection_rate > 0.5 else 'medium',
                    'detected_at': datetime.utcnow().isoformat()
                })
            
            # 4. Unusual transaction patterns
            large_transactions_result = await self.db.execute(
                select(
                    User.id,
                    User.username,
                    Transaction.amount,
                    Transaction.created_at
                ).join(Transaction, or_(Transaction.creator_id == User.id, Transaction.booster_id == User.id))
                .where(Transaction.amount > 1000)  # Transactions over $1000
                .order_by(Transaction.created_at.desc())
                .limit(10)
            )
            
            for row in large_transactions_result:
                suspicious_activities.append({
                    'type': 'large_transaction',
                    'user_id': str(row.id),
                    'username': row.username,
                    'details': {
                        'transaction_amount': float(row.amount),
                        'transaction_date': row.created_at.isoformat()
                    },
                    'severity': 'medium',
                    'detected_at': datetime.utcnow().isoformat()
                })
            
            return {
                'suspicious_activities': suspicious_activities[:limit],
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': len(suspicious_activities),
                    'has_next': len(suspicious_activities) > limit,
                    'has_prev': page > 1
                }
            }

        except Exception as e:
            logger.error(f"Failed to get suspicious activities: {e}")
            raise

    async def get_audit_logs(
        self,
        user_id: Optional[uuid.UUID] = None,
        action: Optional[str] = None,
        resource_type: Optional[str] = None,
        page: int = 1,
        limit: int = 20
    ) -> Dict[str, Any]:
        """Get audit logs with filtering"""
        try:
            offset = (page - 1) * limit
            
            # Build query
            query = select(AuditLog, User.username).join(User, AuditLog.user_id == User.id, isouter=True)
            conditions = []
            
            if user_id:
                conditions.append(AuditLog.user_id == user_id)
            if action:
                conditions.append(AuditLog.action.ilike(f"%{action}%"))
            if resource_type:
                conditions.append(AuditLog.resource_type == resource_type)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            # Get logs with pagination
            result = await self.db.execute(
                query.order_by(AuditLog.created_at.desc()).offset(offset).limit(limit)
            )
            logs = result.all()
            
            # Get total count
            count_query = select(func.count(AuditLog.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            count_result = await self.db.execute(count_query)
            total = count_result.scalar()
            
            # Format response
            logs_data = []
            for log, username in logs:
                logs_data.append({
                    'id': str(log.id),
                    'user_id': str(log.user_id) if log.user_id else None,
                    'username': username,
                    'action': log.action,
                    'resource_type': log.resource_type,
                    'resource_id': str(log.resource_id) if log.resource_id else None,
                    'old_values': log.old_values,
                    'new_values': log.new_values,
                    'ip_address': str(log.ip_address) if log.ip_address else None,
                    'user_agent': log.user_agent,
                    'created_at': log.created_at.isoformat()
                })
            
            return {
                'audit_logs': logs_data,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total,
                    'has_next': (page * limit) < total,
                    'has_prev': page > 1,
                    'total_pages': (total + limit - 1) // limit
                }
            }

        except Exception as e:
            logger.error(f"Failed to get audit logs: {e}")
            raise

    async def get_platform_health(self) -> Dict[str, Any]:
        """Get platform health metrics"""
        try:
            # Active users (logged in within last 7 days)
            seven_days_ago = datetime.utcnow() - timedelta(days=7)
            active_users_result = await self.db.execute(
                select(func.count(User.id)).where(User.last_login >= seven_days_ago)
            )
            active_users = active_users_result.scalar() or 0
            
            # Active campaigns
            active_campaigns_result = await self.db.execute(
                select(func.count(Campaign.id)).where(Campaign.status == CampaignStatus.ACTIVE)
            )
            active_campaigns = active_campaigns_result.scalar() or 0
            
            # Pending tasks
            pending_tasks_result = await self.db.execute(
                select(func.count(Task.id)).where(Task.status == TaskStatus.PENDING)
            )
            pending_tasks = pending_tasks_result.scalar() or 0
            
            # Average task completion time (in hours)
            avg_completion_result = await self.db.execute(
                select(
                    func.avg(
                        func.extract('epoch', Task.completed_at - Task.assigned_at) / 3600
                    )
                ).where(
                    and_(
                        Task.status == TaskStatus.COMPLETED,
                        Task.assigned_at.isnot(None),
                        Task.completed_at.isnot(None),
                        Task.completed_at >= seven_days_ago
                    )
                )
            )
            avg_completion_hours = avg_completion_result.scalar() or 0
            
            # System health score (0-100)
            health_score = min(100, max(0, 
                (active_users / 100 * 30) +  # 30% weight for active users
                (active_campaigns / 50 * 25) +  # 25% weight for active campaigns
                (min(pending_tasks, 1000) / 1000 * 20) +  # 20% weight for pending tasks
                (max(0, 24 - avg_completion_hours) / 24 * 25)  # 25% weight for completion time
            ))
            
            return {
                'active_users_7_days': active_users,
                'active_campaigns': active_campaigns,
                'pending_tasks': pending_tasks,
                'avg_completion_hours': round(float(avg_completion_hours), 2),
                'health_score': round(health_score, 1),
                'status': 'healthy' if health_score > 80 else 'warning' if health_score > 60 else 'critical',
                'last_updated': datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get platform health: {e}")
            raise
