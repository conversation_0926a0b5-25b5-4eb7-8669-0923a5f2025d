import stripe
from typing import Optional, Dict, Any
from decimal import Decimal
from app.core.config import settings
from app.models.user import User
from app.models.transaction import Transaction, TransactionStatus
import logging

logger = logging.getLogger(__name__)

# Configure Stripe
stripe.api_key = settings.STRIPE_SECRET_KEY


class StripeService:
    def __init__(self):
        self.platform_fee_percentage = settings.STRIPE_PLATFORM_FEE_PERCENTAGE

    async def create_customer(self, user: User) -> str:
        """Create a Stripe customer for a user"""
        try:
            customer = stripe.Customer.create(
                email=user.email,
                name=f"{user.first_name} {user.last_name}",
                metadata={
                    'user_id': str(user.id),
                    'username': user.username,
                    'role': user.role
                }
            )
            return customer.id
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create Stripe customer: {e}")
            raise Exception(f"Payment system error: {str(e)}")

    async def create_connected_account(self, user: User) -> str:
        """Create a Stripe Connect account for content boosters to receive payments"""
        try:
            account = stripe.Account.create(
                type='express',
                country='US',  # This should be dynamic based on user's country
                email=user.email,
                capabilities={
                    'card_payments': {'requested': True},
                    'transfers': {'requested': True},
                },
                business_type='individual',
                individual={
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                },
                metadata={
                    'user_id': str(user.id),
                    'username': user.username,
                }
            )
            return account.id
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create Stripe Connect account: {e}")
            raise Exception(f"Payment system error: {str(e)}")

    async def create_account_link(self, account_id: str, refresh_url: str, return_url: str) -> str:
        """Create an account link for onboarding"""
        try:
            account_link = stripe.AccountLink.create(
                account=account_id,
                refresh_url=refresh_url,
                return_url=return_url,
                type='account_onboarding',
            )
            return account_link.url
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create account link: {e}")
            raise Exception(f"Payment system error: {str(e)}")

    async def create_payment_intent(
        self, 
        amount: Decimal, 
        customer_id: str, 
        metadata: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Create a payment intent for escrow"""
        try:
            # Convert amount to cents
            amount_cents = int(amount * 100)
            
            payment_intent = stripe.PaymentIntent.create(
                amount=amount_cents,
                currency='usd',
                customer=customer_id,
                capture_method='manual',  # Hold funds in escrow
                metadata=metadata or {},
                automatic_payment_methods={
                    'enabled': True,
                },
            )
            
            return {
                'id': payment_intent.id,
                'client_secret': payment_intent.client_secret,
                'status': payment_intent.status,
                'amount': amount,
            }
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create payment intent: {e}")
            raise Exception(f"Payment system error: {str(e)}")

    async def capture_payment_intent(self, payment_intent_id: str) -> bool:
        """Capture a payment intent (release from escrow)"""
        try:
            payment_intent = stripe.PaymentIntent.capture(payment_intent_id)
            return payment_intent.status == 'succeeded'
        except stripe.error.StripeError as e:
            logger.error(f"Failed to capture payment intent: {e}")
            return False

    async def cancel_payment_intent(self, payment_intent_id: str) -> bool:
        """Cancel a payment intent (refund from escrow)"""
        try:
            payment_intent = stripe.PaymentIntent.cancel(payment_intent_id)
            return payment_intent.status == 'canceled'
        except stripe.error.StripeError as e:
            logger.error(f"Failed to cancel payment intent: {e}")
            return False

    async def create_transfer(
        self, 
        amount: Decimal, 
        destination_account_id: str, 
        metadata: Dict[str, Any] = None
    ) -> str:
        """Transfer funds to a connected account"""
        try:
            # Calculate platform fee
            platform_fee = amount * (self.platform_fee_percentage / 100)
            transfer_amount = amount - platform_fee
            
            # Convert to cents
            transfer_amount_cents = int(transfer_amount * 100)
            
            transfer = stripe.Transfer.create(
                amount=transfer_amount_cents,
                currency='usd',
                destination=destination_account_id,
                metadata=metadata or {},
            )
            
            return transfer.id
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create transfer: {e}")
            raise Exception(f"Payment system error: {str(e)}")

    async def create_refund(self, payment_intent_id: str, amount: Optional[Decimal] = None) -> str:
        """Create a refund for a payment"""
        try:
            refund_data = {'payment_intent': payment_intent_id}
            
            if amount:
                refund_data['amount'] = int(amount * 100)
            
            refund = stripe.Refund.create(**refund_data)
            return refund.id
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create refund: {e}")
            raise Exception(f"Payment system error: {str(e)}")

    async def get_account_status(self, account_id: str) -> Dict[str, Any]:
        """Get the status of a connected account"""
        try:
            account = stripe.Account.retrieve(account_id)
            return {
                'id': account.id,
                'charges_enabled': account.charges_enabled,
                'payouts_enabled': account.payouts_enabled,
                'details_submitted': account.details_submitted,
                'requirements': account.requirements,
            }
        except stripe.error.StripeError as e:
            logger.error(f"Failed to get account status: {e}")
            raise Exception(f"Payment system error: {str(e)}")

    async def create_setup_intent(self, customer_id: str) -> Dict[str, Any]:
        """Create a setup intent for saving payment methods"""
        try:
            setup_intent = stripe.SetupIntent.create(
                customer=customer_id,
                automatic_payment_methods={
                    'enabled': True,
                },
            )
            
            return {
                'id': setup_intent.id,
                'client_secret': setup_intent.client_secret,
                'status': setup_intent.status,
            }
        except stripe.error.StripeError as e:
            logger.error(f"Failed to create setup intent: {e}")
            raise Exception(f"Payment system error: {str(e)}")

    async def get_payment_methods(self, customer_id: str) -> list:
        """Get saved payment methods for a customer"""
        try:
            payment_methods = stripe.PaymentMethod.list(
                customer=customer_id,
                type='card',
            )
            return payment_methods.data
        except stripe.error.StripeError as e:
            logger.error(f"Failed to get payment methods: {e}")
            return []

    def calculate_platform_fee(self, amount: Decimal) -> Decimal:
        """Calculate platform fee"""
        return amount * (self.platform_fee_percentage / 100)

    def calculate_net_amount(self, amount: Decimal) -> Decimal:
        """Calculate net amount after platform fee"""
        return amount - self.calculate_platform_fee(amount)

    async def handle_webhook(self, payload: str, sig_header: str) -> Dict[str, Any]:
        """Handle Stripe webhook events"""
        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, settings.STRIPE_WEBHOOK_SECRET
            )
            
            logger.info(f"Received Stripe webhook: {event['type']}")
            
            return {
                'event_type': event['type'],
                'event_id': event['id'],
                'data': event['data'],
            }
        except ValueError as e:
            logger.error(f"Invalid payload in webhook: {e}")
            raise Exception("Invalid payload")
        except stripe.error.SignatureVerificationError as e:
            logger.error(f"Invalid signature in webhook: {e}")
            raise Exception("Invalid signature")


# Global instance
stripe_service = StripeService()
