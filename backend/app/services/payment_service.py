from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_
from decimal import Decimal
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid

from app.models.user import User
from app.models.transaction import Transaction, TransactionStatus
from app.models.campaign import Campaign
from app.models.task import Task
from app.services.stripe_service import stripe_service
import logging

logger = logging.getLogger(__name__)


class PaymentService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def get_user_balance(self, user_id: uuid.UUID) -> Dict[str, Any]:
        """Get user's balance information"""
        try:
            # Get user
            result = await self.db.execute(select(User).where(User.id == user_id))
            user = result.scalar_one_or_none()
            
            if not user:
                raise ValueError("User not found")
            
            # Get pending transactions
            pending_result = await self.db.execute(
                select(Transaction).where(
                    and_(
                        Transaction.creator_id == user_id if user.role == 'content_creator' else Transaction.booster_id == user_id,
                        Transaction.status == TransactionStatus.HELD
                    )
                )
            )
            pending_transactions = pending_result.scalars().all()
            pending_amount = sum(t.amount for t in pending_transactions)
            
            return {
                'available_balance': float(user.total_earnings) if user.role == 'content_booster' else 0,
                'total_spent': float(user.total_spent),
                'total_earnings': float(user.total_earnings),
                'pending_amount': float(pending_amount),
                'stripe_customer_id': user.stripe_customer_id,
                'stripe_account_id': user.stripe_account_id,
            }
        except Exception as e:
            logger.error(f"Failed to get user balance: {e}")
            raise

    async def create_deposit(self, user_id: uuid.UUID, amount: Decimal, payment_method_id: Optional[str] = None) -> Dict[str, Any]:
        """Create a deposit for content creator"""
        try:
            # Get user
            result = await self.db.execute(select(User).where(User.id == user_id))
            user = result.scalar_one_or_none()
            
            if not user:
                raise ValueError("User not found")
            
            if user.role != 'content_creator':
                raise ValueError("Only content creators can deposit funds")
            
            # Ensure user has Stripe customer ID
            customer_id = user.stripe_customer_id
            if not customer_id:
                customer_id = await stripe_service.create_customer(user)
                await self.db.execute(
                    update(User).where(User.id == user_id).values(stripe_customer_id=customer_id)
                )
                await self.db.commit()
            
            # Create payment intent
            payment_intent = await stripe_service.create_payment_intent(
                amount=amount,
                customer_id=customer_id,
                metadata={
                    'user_id': str(user_id),
                    'type': 'deposit'
                }
            )
            
            # Create transaction record
            transaction = Transaction(
                creator_id=user_id,
                amount=amount,
                fee=Decimal('0'),
                net_amount=amount,
                status=TransactionStatus.PENDING,
                stripe_payment_intent_id=payment_intent['id']
            )
            
            self.db.add(transaction)
            await self.db.commit()
            await self.db.refresh(transaction)
            
            return {
                'transaction_id': str(transaction.id),
                'payment_intent': payment_intent,
                'amount': float(amount)
            }
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create deposit: {e}")
            raise

    async def create_withdrawal(self, user_id: uuid.UUID, amount: Decimal) -> Dict[str, Any]:
        """Create a withdrawal for content booster"""
        try:
            # Get user
            result = await self.db.execute(select(User).where(User.id == user_id))
            user = result.scalar_one_or_none()
            
            if not user:
                raise ValueError("User not found")
            
            if user.role != 'content_booster':
                raise ValueError("Only content boosters can withdraw earnings")
            
            if user.total_earnings < amount:
                raise ValueError("Insufficient balance")
            
            # Ensure user has Stripe Connect account
            account_id = user.stripe_account_id
            if not account_id:
                raise ValueError("Stripe account not set up. Please complete account setup first.")
            
            # Check account status
            account_status = await stripe_service.get_account_status(account_id)
            if not account_status['payouts_enabled']:
                raise ValueError("Account not ready for payouts. Please complete account setup.")
            
            # Create transfer
            transfer_id = await stripe_service.create_transfer(
                amount=amount,
                destination_account_id=account_id,
                metadata={
                    'user_id': str(user_id),
                    'type': 'withdrawal'
                }
            )
            
            # Create transaction record
            platform_fee = stripe_service.calculate_platform_fee(amount)
            net_amount = amount - platform_fee
            
            transaction = Transaction(
                booster_id=user_id,
                amount=amount,
                fee=platform_fee,
                net_amount=net_amount,
                status=TransactionStatus.COMPLETED,
                stripe_transfer_id=transfer_id
            )
            
            self.db.add(transaction)
            
            # Update user balance
            await self.db.execute(
                update(User).where(User.id == user_id).values(
                    total_earnings=User.total_earnings - amount
                )
            )
            
            await self.db.commit()
            await self.db.refresh(transaction)
            
            return {
                'transaction_id': str(transaction.id),
                'transfer_id': transfer_id,
                'amount': float(amount),
                'fee': float(platform_fee),
                'net_amount': float(net_amount)
            }
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create withdrawal: {e}")
            raise

    async def get_user_transactions(self, user_id: uuid.UUID, page: int = 1, limit: int = 20) -> Dict[str, Any]:
        """Get user's transaction history"""
        try:
            offset = (page - 1) * limit
            
            # Get transactions where user is either creator or booster
            result = await self.db.execute(
                select(Transaction).where(
                    (Transaction.creator_id == user_id) | (Transaction.booster_id == user_id)
                ).order_by(Transaction.created_at.desc()).offset(offset).limit(limit)
            )
            transactions = result.scalars().all()
            
            # Get total count
            count_result = await self.db.execute(
                select(Transaction).where(
                    (Transaction.creator_id == user_id) | (Transaction.booster_id == user_id)
                )
            )
            total = len(count_result.scalars().all())
            
            transaction_data = []
            for t in transactions:
                transaction_data.append({
                    'id': str(t.id),
                    'amount': float(t.amount),
                    'fee': float(t.fee),
                    'net_amount': float(t.net_amount),
                    'status': t.status,
                    'created_at': t.created_at.isoformat(),
                    'type': 'deposit' if t.creator_id == user_id else 'earning'
                })
            
            return {
                'transactions': transaction_data,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total,
                    'has_next': (page * limit) < total,
                    'has_prev': page > 1
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get user transactions: {e}")
            raise

    async def process_task_payment(self, task_id: uuid.UUID, campaign_id: uuid.UUID) -> bool:
        """Process payment when task is completed and approved"""
        try:
            # Get task and campaign details
            task_result = await self.db.execute(select(Task).where(Task.id == task_id))
            task = task_result.scalar_one_or_none()
            
            campaign_result = await self.db.execute(select(Campaign).where(Campaign.id == campaign_id))
            campaign = campaign_result.scalar_one_or_none()
            
            if not task or not campaign:
                raise ValueError("Task or campaign not found")
            
            # Create transaction for task completion
            platform_fee = stripe_service.calculate_platform_fee(campaign.price_per_task)
            net_amount = campaign.price_per_task - platform_fee
            
            transaction = Transaction(
                campaign_id=campaign_id,
                task_id=task_id,
                creator_id=campaign.creator_id,
                booster_id=task.booster_id,
                amount=campaign.price_per_task,
                fee=platform_fee,
                net_amount=net_amount,
                status=TransactionStatus.COMPLETED
            )
            
            self.db.add(transaction)
            
            # Update booster earnings
            if task.booster_id:
                await self.db.execute(
                    update(User).where(User.id == task.booster_id).values(
                        total_earnings=User.total_earnings + net_amount
                    )
                )
            
            # Update campaign budget
            await self.db.execute(
                update(Campaign).where(Campaign.id == campaign_id).values(
                    remaining_budget=Campaign.remaining_budget - campaign.price_per_task
                )
            )
            
            await self.db.commit()
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to process task payment: {e}")
            return False

    async def process_webhook_event(self, event_data: Dict[str, Any]) -> None:
        """Process Stripe webhook events"""
        try:
            event_type = event_data['event_type']
            data = event_data['data']['object']
            
            if event_type == 'payment_intent.succeeded':
                await self._handle_payment_succeeded(data)
            elif event_type == 'payment_intent.payment_failed':
                await self._handle_payment_failed(data)
            elif event_type == 'transfer.created':
                await self._handle_transfer_created(data)
            
        except Exception as e:
            logger.error(f"Failed to process webhook event: {e}")

    async def _handle_payment_succeeded(self, payment_intent_data: Dict[str, Any]) -> None:
        """Handle successful payment"""
        payment_intent_id = payment_intent_data['id']
        
        # Update transaction status
        await self.db.execute(
            update(Transaction).where(
                Transaction.stripe_payment_intent_id == payment_intent_id
            ).values(status=TransactionStatus.COMPLETED)
        )
        await self.db.commit()

    async def _handle_payment_failed(self, payment_intent_data: Dict[str, Any]) -> None:
        """Handle failed payment"""
        payment_intent_id = payment_intent_data['id']
        
        # Update transaction status
        await self.db.execute(
            update(Transaction).where(
                Transaction.stripe_payment_intent_id == payment_intent_id
            ).values(status=TransactionStatus.REFUNDED)
        )
        await self.db.commit()

    async def _handle_transfer_created(self, transfer_data: Dict[str, Any]) -> None:
        """Handle transfer creation"""
        transfer_id = transfer_data['id']
        
        # Update transaction with transfer ID
        await self.db.execute(
            update(Transaction).where(
                Transaction.stripe_transfer_id == transfer_id
            ).values(status=TransactionStatus.COMPLETED)
        )
        await self.db.commit()
