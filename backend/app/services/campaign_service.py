from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, func
from sqlalchemy.orm import selectinload
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from decimal import Decimal
import uuid

from app.models.user import User
from app.models.campaign import Campaign, CampaignStatus
from app.models.task import Task, TaskStatus
from app.models.transaction import Transaction
from app.schemas.campaign import CampaignCreate, CampaignUpdate
import logging

logger = logging.getLogger(__name__)


class CampaignService:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def create_campaign(self, campaign_data: CampaignCreate, creator_id: uuid.UUID) -> Campaign:
        """Create a new campaign"""
        try:
            # Calculate total budget
            total_budget = campaign_data.price_per_task * campaign_data.target_quantity
            
            # Create campaign
            campaign = Campaign(
                creator_id=creator_id,
                social_account_id=campaign_data.social_account_id,
                title=campaign_data.title,
                description=campaign_data.description,
                platform=campaign_data.platform,
                engagement_type=campaign_data.engagement_type,
                target_url=campaign_data.target_url,
                target_quantity=campaign_data.target_quantity,
                price_per_task=campaign_data.price_per_task,
                total_budget=total_budget,
                remaining_budget=total_budget,
                end_date=campaign_data.end_date,
                max_tasks_per_user=campaign_data.max_tasks_per_user,
                min_booster_rating=campaign_data.min_booster_rating,
                instructions=campaign_data.instructions,
                proof_required=campaign_data.proof_required,
                auto_approve=campaign_data.auto_approve,
                status=CampaignStatus.DRAFT
            )
            
            self.db.add(campaign)
            await self.db.commit()
            await self.db.refresh(campaign)
            
            return campaign
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create campaign: {e}")
            raise

    async def get_campaign_by_id(self, campaign_id: uuid.UUID) -> Optional[Campaign]:
        """Get campaign by ID"""
        result = await self.db.execute(
            select(Campaign).where(Campaign.id == campaign_id)
        )
        return result.scalar_one_or_none()

    async def get_campaigns(
        self, 
        creator_id: Optional[uuid.UUID] = None,
        status: Optional[CampaignStatus] = None,
        platform: Optional[str] = None,
        page: int = 1,
        limit: int = 20
    ) -> Dict[str, Any]:
        """Get campaigns with filtering and pagination"""
        try:
            offset = (page - 1) * limit
            
            # Build query
            query = select(Campaign)
            conditions = []
            
            if creator_id:
                conditions.append(Campaign.creator_id == creator_id)
            if status:
                conditions.append(Campaign.status == status)
            if platform:
                conditions.append(Campaign.platform == platform)
            
            if conditions:
                query = query.where(and_(*conditions))
            
            # Get campaigns with pagination
            result = await self.db.execute(
                query.order_by(Campaign.created_at.desc()).offset(offset).limit(limit)
            )
            campaigns = result.scalars().all()
            
            # Get total count
            count_query = select(func.count(Campaign.id))
            if conditions:
                count_query = count_query.where(and_(*conditions))
            
            count_result = await self.db.execute(count_query)
            total = count_result.scalar()
            
            # Convert to dict format
            campaign_data = []
            for campaign in campaigns:
                campaign_dict = {
                    'id': str(campaign.id),
                    'creator_id': str(campaign.creator_id),
                    'social_account_id': str(campaign.social_account_id),
                    'title': campaign.title,
                    'description': campaign.description,
                    'platform': campaign.platform,
                    'engagement_type': campaign.engagement_type,
                    'target_url': campaign.target_url,
                    'target_quantity': campaign.target_quantity,
                    'completed_quantity': campaign.completed_quantity,
                    'price_per_task': float(campaign.price_per_task),
                    'total_budget': float(campaign.total_budget),
                    'remaining_budget': float(campaign.remaining_budget),
                    'status': campaign.status,
                    'start_date': campaign.start_date.isoformat() if campaign.start_date else None,
                    'end_date': campaign.end_date.isoformat() if campaign.end_date else None,
                    'max_tasks_per_user': campaign.max_tasks_per_user,
                    'min_booster_rating': float(campaign.min_booster_rating),
                    'instructions': campaign.instructions,
                    'proof_required': campaign.proof_required,
                    'auto_approve': campaign.auto_approve,
                    'created_at': campaign.created_at.isoformat(),
                    'updated_at': campaign.updated_at.isoformat(),
                    'progress_percentage': (campaign.completed_quantity / campaign.target_quantity * 100) if campaign.target_quantity > 0 else 0
                }
                campaign_data.append(campaign_dict)
            
            return {
                'campaigns': campaign_data,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total,
                    'has_next': (page * limit) < total,
                    'has_prev': page > 1,
                    'total_pages': (total + limit - 1) // limit
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get campaigns: {e}")
            raise

    async def update_campaign(self, campaign_id: uuid.UUID, campaign_data: CampaignUpdate, creator_id: uuid.UUID) -> Optional[Campaign]:
        """Update campaign"""
        try:
            # Get existing campaign
            campaign = await self.get_campaign_by_id(campaign_id)
            if not campaign:
                return None
            
            # Check ownership
            if campaign.creator_id != creator_id:
                raise ValueError("Not authorized to update this campaign")
            
            # Check if campaign can be updated
            if campaign.status in [CampaignStatus.COMPLETED, CampaignStatus.CANCELLED]:
                raise ValueError("Cannot update completed or cancelled campaigns")
            
            # Update fields
            update_data = campaign_data.dict(exclude_unset=True)
            
            # Recalculate budget if price or quantity changed
            if 'price_per_task' in update_data or 'target_quantity' in update_data:
                new_price = update_data.get('price_per_task', campaign.price_per_task)
                new_quantity = update_data.get('target_quantity', campaign.target_quantity)
                new_total_budget = new_price * new_quantity
                
                # Calculate remaining budget based on completed tasks
                spent_budget = campaign.price_per_task * campaign.completed_quantity
                update_data['total_budget'] = new_total_budget
                update_data['remaining_budget'] = new_total_budget - spent_budget
            
            if update_data:
                await self.db.execute(
                    update(Campaign)
                    .where(Campaign.id == campaign_id)
                    .values(**update_data, updated_at=datetime.utcnow())
                )
                await self.db.commit()
                
                # Refresh campaign
                await self.db.refresh(campaign)
            
            return campaign
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update campaign: {e}")
            raise

    async def delete_campaign(self, campaign_id: uuid.UUID, creator_id: uuid.UUID) -> bool:
        """Delete campaign (only if no tasks are in progress)"""
        try:
            # Get campaign
            campaign = await self.get_campaign_by_id(campaign_id)
            if not campaign:
                return False
            
            # Check ownership
            if campaign.creator_id != creator_id:
                raise ValueError("Not authorized to delete this campaign")
            
            # Check if campaign has active tasks
            active_tasks_result = await self.db.execute(
                select(Task).where(
                    and_(
                        Task.campaign_id == campaign_id,
                        Task.status.in_([TaskStatus.IN_PROGRESS, TaskStatus.COMPLETED])
                    )
                )
            )
            active_tasks = active_tasks_result.scalars().all()
            
            if active_tasks:
                raise ValueError("Cannot delete campaign with active tasks")
            
            # Delete campaign and related tasks
            await self.db.execute(delete(Task).where(Task.campaign_id == campaign_id))
            await self.db.execute(delete(Campaign).where(Campaign.id == campaign_id))
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to delete campaign: {e}")
            raise

    async def activate_campaign(self, campaign_id: uuid.UUID, creator_id: uuid.UUID) -> bool:
        """Activate a campaign and generate tasks"""
        try:
            # Get campaign
            campaign = await self.get_campaign_by_id(campaign_id)
            if not campaign:
                return False
            
            # Check ownership
            if campaign.creator_id != creator_id:
                raise ValueError("Not authorized to activate this campaign")
            
            # Check if campaign can be activated
            if campaign.status != CampaignStatus.DRAFT:
                raise ValueError("Only draft campaigns can be activated")
            
            # Check if creator has sufficient budget (implement payment check)
            # This would integrate with the payment system
            
            # Generate tasks for the campaign
            tasks_created = await self._generate_tasks_for_campaign(campaign)
            
            # Update campaign status
            await self.db.execute(
                update(Campaign)
                .where(Campaign.id == campaign_id)
                .values(
                    status=CampaignStatus.ACTIVE,
                    start_date=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
            )
            await self.db.commit()
            
            logger.info(f"Campaign {campaign_id} activated with {tasks_created} tasks")
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to activate campaign: {e}")
            raise

    async def pause_campaign(self, campaign_id: uuid.UUID, creator_id: uuid.UUID) -> bool:
        """Pause an active campaign"""
        try:
            campaign = await self.get_campaign_by_id(campaign_id)
            if not campaign:
                return False
            
            if campaign.creator_id != creator_id:
                raise ValueError("Not authorized to pause this campaign")
            
            if campaign.status != CampaignStatus.ACTIVE:
                raise ValueError("Only active campaigns can be paused")
            
            await self.db.execute(
                update(Campaign)
                .where(Campaign.id == campaign_id)
                .values(status=CampaignStatus.PAUSED, updated_at=datetime.utcnow())
            )
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to pause campaign: {e}")
            raise

    async def resume_campaign(self, campaign_id: uuid.UUID, creator_id: uuid.UUID) -> bool:
        """Resume a paused campaign"""
        try:
            campaign = await self.get_campaign_by_id(campaign_id)
            if not campaign:
                return False
            
            if campaign.creator_id != creator_id:
                raise ValueError("Not authorized to resume this campaign")
            
            if campaign.status != CampaignStatus.PAUSED:
                raise ValueError("Only paused campaigns can be resumed")
            
            await self.db.execute(
                update(Campaign)
                .where(Campaign.id == campaign_id)
                .values(status=CampaignStatus.ACTIVE, updated_at=datetime.utcnow())
            )
            await self.db.commit()
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to resume campaign: {e}")
            raise

    async def get_campaign_analytics(self, campaign_id: uuid.UUID, creator_id: uuid.UUID) -> Dict[str, Any]:
        """Get campaign analytics and metrics"""
        try:
            # Get campaign
            campaign = await self.get_campaign_by_id(campaign_id)
            if not campaign:
                raise ValueError("Campaign not found")
            
            if campaign.creator_id != creator_id:
                raise ValueError("Not authorized to view this campaign")
            
            # Get task statistics
            task_stats_result = await self.db.execute(
                select(
                    Task.status,
                    func.count(Task.id).label('count')
                ).where(Task.campaign_id == campaign_id).group_by(Task.status)
            )
            task_stats = {row.status: row.count for row in task_stats_result}
            
            # Get completion rate over time (last 30 days)
            thirty_days_ago = datetime.utcnow() - timedelta(days=30)
            daily_completions_result = await self.db.execute(
                select(
                    func.date(Task.completed_at).label('date'),
                    func.count(Task.id).label('count')
                ).where(
                    and_(
                        Task.campaign_id == campaign_id,
                        Task.status == TaskStatus.COMPLETED,
                        Task.completed_at >= thirty_days_ago
                    )
                ).group_by(func.date(Task.completed_at)).order_by(func.date(Task.completed_at))
            )
            daily_completions = [
                {'date': row.date.isoformat(), 'count': row.count}
                for row in daily_completions_result
            ]
            
            # Calculate metrics
            total_tasks = sum(task_stats.values())
            completed_tasks = task_stats.get(TaskStatus.COMPLETED, 0)
            completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
            
            return {
                'campaign_id': str(campaign_id),
                'title': campaign.title,
                'status': campaign.status,
                'progress': {
                    'target_quantity': campaign.target_quantity,
                    'completed_quantity': campaign.completed_quantity,
                    'completion_percentage': (campaign.completed_quantity / campaign.target_quantity * 100) if campaign.target_quantity > 0 else 0
                },
                'budget': {
                    'total_budget': float(campaign.total_budget),
                    'remaining_budget': float(campaign.remaining_budget),
                    'spent_budget': float(campaign.total_budget - campaign.remaining_budget)
                },
                'task_statistics': task_stats,
                'completion_rate': completion_rate,
                'daily_completions': daily_completions,
                'average_completion_time': await self._calculate_average_completion_time(campaign_id),
                'top_boosters': await self._get_top_boosters_for_campaign(campaign_id)
            }
            
        except Exception as e:
            logger.error(f"Failed to get campaign analytics: {e}")
            raise

    async def _generate_tasks_for_campaign(self, campaign: Campaign) -> int:
        """Generate individual tasks for a campaign"""
        tasks_to_create = campaign.target_quantity - campaign.completed_quantity
        tasks_created = 0
        
        for _ in range(tasks_to_create):
            task = Task(
                campaign_id=campaign.id,
                status=TaskStatus.PENDING
            )
            self.db.add(task)
            tasks_created += 1
        
        return tasks_created

    async def _calculate_average_completion_time(self, campaign_id: uuid.UUID) -> Optional[float]:
        """Calculate average time to complete tasks in hours"""
        try:
            result = await self.db.execute(
                select(
                    func.avg(
                        func.extract('epoch', Task.completed_at - Task.assigned_at) / 3600
                    ).label('avg_hours')
                ).where(
                    and_(
                        Task.campaign_id == campaign_id,
                        Task.status == TaskStatus.COMPLETED,
                        Task.assigned_at.isnot(None),
                        Task.completed_at.isnot(None)
                    )
                )
            )
            avg_hours = result.scalar()
            return float(avg_hours) if avg_hours else None
        except Exception:
            return None

    async def _get_top_boosters_for_campaign(self, campaign_id: uuid.UUID) -> List[Dict[str, Any]]:
        """Get top performing boosters for a campaign"""
        try:
            result = await self.db.execute(
                select(
                    User.id,
                    User.username,
                    User.rating,
                    func.count(Task.id).label('completed_tasks')
                ).join(Task, Task.booster_id == User.id)
                .where(
                    and_(
                        Task.campaign_id == campaign_id,
                        Task.status == TaskStatus.COMPLETED
                    )
                ).group_by(User.id, User.username, User.rating)
                .order_by(func.count(Task.id).desc())
                .limit(5)
            )
            
            return [
                {
                    'user_id': str(row.id),
                    'username': row.username,
                    'rating': float(row.rating),
                    'completed_tasks': row.completed_tasks
                }
                for row in result
            ]
        except Exception:
            return []
