from fastapi import HTTPException
from typing import Any, Optional, List
import time


def success_response(
    data: Any = None,
    message: str = "Success",
    meta: Optional[dict] = None
) -> dict:
    """Create a standardized success response"""
    response = {
        "success": True,
        "data": data,
        "message": message,
        "errors": [],
        "meta": {
            "timestamp": time.time(),
            **(meta or {})
        }
    }
    return response


def error_response(
    message: str = "An error occurred",
    errors: Optional[List[str]] = None,
    status_code: int = 400,
    data: Any = None,
    meta: Optional[dict] = None
) -> HTTPException:
    """Create a standardized error response"""
    response_data = {
        "success": False,
        "data": data,
        "message": message,
        "errors": errors or [message],
        "meta": {
            "timestamp": time.time(),
            **(meta or {})
        }
    }
    raise HTTPException(status_code=status_code, detail=response_data)


def paginated_response(
    items: List[Any],
    total: int,
    page: int,
    limit: int,
    message: str = "Data retrieved successfully"
) -> dict:
    """Create a paginated response"""
    has_next = (page * limit) < total
    has_prev = page > 1
    
    return success_response(
        data={
            "items": items,
            "pagination": {
                "total": total,
                "page": page,
                "limit": limit,
                "has_next": has_next,
                "has_prev": has_prev,
                "total_pages": (total + limit - 1) // limit
            }
        },
        message=message
    )
