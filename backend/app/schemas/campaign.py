from pydantic import BaseModel, <PERSON>, validator
from typing import Optional
from datetime import datetime
from decimal import Decimal
from app.models.user import SocialPlatform
from app.models.campaign import EngagementType, CampaignStatus
import uuid


class CampaignBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    platform: SocialPlatform
    engagement_type: EngagementType
    target_url: str = Field(..., min_length=1)
    target_quantity: int = Field(..., gt=0)
    price_per_task: Decimal = Field(..., gt=0, decimal_places=2)
    end_date: Optional[datetime] = None
    max_tasks_per_user: int = Field(default=1, ge=1)
    min_booster_rating: Decimal = Field(default=0.00, ge=0.00, le=5.00)
    instructions: Optional[str] = None
    proof_required: bool = True
    auto_approve: bool = False


class CampaignCreate(CampaignBase):
    social_account_id: uuid.UUID

    @validator('end_date')
    def validate_end_date(cls, v):
        if v and v <= datetime.utcnow():
            raise ValueError('End date must be in the future')
        return v


class CampaignUpdate(BaseModel):
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = None
    target_quantity: Optional[int] = Field(None, gt=0)
    price_per_task: Optional[Decimal] = Field(None, gt=0, decimal_places=2)
    end_date: Optional[datetime] = None
    max_tasks_per_user: Optional[int] = Field(None, ge=1)
    min_booster_rating: Optional[Decimal] = Field(None, ge=0.00, le=5.00)
    instructions: Optional[str] = None
    proof_required: Optional[bool] = None
    auto_approve: Optional[bool] = None


class CampaignResponse(CampaignBase):
    id: uuid.UUID
    creator_id: uuid.UUID
    social_account_id: uuid.UUID
    completed_quantity: int
    total_budget: Decimal
    remaining_budget: Decimal
    status: CampaignStatus
    start_date: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class CampaignListResponse(BaseModel):
    campaigns: list[CampaignResponse]
    total: int
    page: int
    limit: int
    has_next: bool
    has_prev: bool
