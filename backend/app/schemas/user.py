from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional
from datetime import datetime
from decimal import Decimal
from app.models.user import User<PERSON><PERSON>, UserStatus, SocialPlatform
import uuid


class UserBase(BaseModel):
    email: EmailStr
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    username: str = Field(..., min_length=3, max_length=50)
    bio: Optional[str] = None
    phone: Optional[str] = None
    country: Optional[str] = None
    timezone: Optional[str] = None


class UserCreate(UserBase):
    password: str = Field(..., min_length=8, max_length=100)
    role: UserRole = UserRole.CONTENT_BOOSTER

    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v


class UserUpdate(BaseModel):
    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)
    bio: Optional[str] = None
    phone: Optional[str] = None
    country: Optional[str] = None
    timezone: Optional[str] = None


class UserResponse(UserBase):
    id: uuid.UUID
    role: UserRole
    status: UserStatus
    avatar_url: Optional[str] = None
    email_verified: bool
    phone_verified: bool
    rating: Decimal
    total_earnings: Decimal
    total_spent: Decimal
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    email: EmailStr
    password: str


class UserPasswordReset(BaseModel):
    token: str
    new_password: str = Field(..., min_length=8, max_length=100)


class UserPasswordChange(BaseModel):
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=100)


class SocialAccountBase(BaseModel):
    platform: SocialPlatform
    account_url: str = Field(..., min_length=1)
    account_username: str = Field(..., min_length=1, max_length=100)


class SocialAccountCreate(SocialAccountBase):
    pass


class SocialAccountUpdate(BaseModel):
    account_url: Optional[str] = None
    account_username: Optional[str] = None
    followers_count: Optional[int] = None


class SocialAccountResponse(SocialAccountBase):
    id: uuid.UUID
    user_id: uuid.UUID
    account_id: Optional[str] = None
    followers_count: int
    verified: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class AuthResponse(BaseModel):
    user: UserResponse
    tokens: TokenResponse
