from sqlalchemy import Column, String, Boolean, DateTime, Decimal, Enum, Text, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from app.core.database import Base
from app.models.user import SocialPlatform
import uuid
import enum


class EngagementType(str, enum.Enum):
    LIKES = "likes"
    COMMENTS = "comments"
    SHARES = "shares"
    VIEWS = "views"
    WATCH_TIME = "watch_time"
    SUBSCRIBERS = "subscribers"
    FOLLOWS = "follows"


class CampaignStatus(str, enum.Enum):
    DRAFT = "draft"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class Campaign(Base):
    __tablename__ = "campaigns"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    creator_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    social_account_id = Column(UUID(as_uuid=True), nullable=False)
    title = Column(String(200), nullable=False)
    description = Column(Text)
    platform = Column(Enum(SocialPlatform), nullable=False)
    engagement_type = Column(Enum(EngagementType), nullable=False)
    target_url = Column(Text, nullable=False)
    target_quantity = Column(Integer, nullable=False)
    completed_quantity = Column(Integer, default=0)
    price_per_task = Column(Decimal(8, 2), nullable=False)
    total_budget = Column(Decimal(10, 2), nullable=False)
    remaining_budget = Column(Decimal(10, 2), nullable=False)
    status = Column(Enum(CampaignStatus), nullable=False, default=CampaignStatus.DRAFT)
    start_date = Column(DateTime(timezone=True))
    end_date = Column(DateTime(timezone=True))
    max_tasks_per_user = Column(Integer, default=1)
    min_booster_rating = Column(Decimal(3, 2), default=0.00)
    instructions = Column(Text)
    proof_required = Column(Boolean, default=True)
    auto_approve = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<Campaign(id={self.id}, title={self.title}, status={self.status})>"
