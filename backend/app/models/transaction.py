from sqlalchemy import Column, String, DateTime, Decimal, Text, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from app.core.database import Base
import uuid
import enum


class TransactionStatus(str, enum.Enum):
    PENDING = "pending"
    HELD = "held"
    COMPLETED = "completed"
    REFUNDED = "refunded"
    DISPUTED = "disputed"


class Transaction(Base):
    __tablename__ = "transactions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    campaign_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    task_id = Column(UUID(as_uuid=True), index=True)
    creator_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    booster_id = Column(UUID(as_uuid=True), index=True)
    amount = Column(Decimal(10, 2), nullable=False)
    fee = Column(Decimal(10, 2), default=0.00)
    net_amount = Column(Decimal(10, 2), nullable=False)
    status = Column(Enum(TransactionStatus), nullable=False, default=TransactionStatus.PENDING)
    stripe_payment_intent_id = Column(String(255))
    stripe_transfer_id = Column(String(255))
    escrow_released_at = Column(DateTime(timezone=True))
    refunded_at = Column(DateTime(timezone=True))
    refund_reason = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<Transaction(id={self.id}, amount={self.amount}, status={self.status})>"
