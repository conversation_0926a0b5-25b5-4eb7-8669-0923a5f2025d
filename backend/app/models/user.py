from sqlalchemy import Column, <PERSON>, <PERSON>ole<PERSON>, DateTime, Decimal, Enum, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from app.core.database import Base
import uuid
import enum


class UserRole(str, enum.Enum):
    CONTENT_CREATOR = "content_creator"
    CONTENT_BOOSTER = "content_booster"
    ADMIN = "admin"


class UserStatus(str, enum.Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"
    BANNED = "banned"
    PENDING_VERIFICATION = "pending_verification"


class SocialPlatform(str, enum.Enum):
    YOUTUBE = "youtube"
    TIKTOK = "tiktok"
    INSTAGRAM = "instagram"
    FACEBOOK = "facebook"
    TWITTER = "twitter"
    LINKEDIN = "linkedin"


class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255))
    first_name = Column(String(100), nullable=False)
    last_name = Column(String(100), nullable=False)
    username = Column(String(50), unique=True, nullable=False, index=True)
    role = Column(Enum(UserRole), nullable=False, default=UserRole.CONTENT_BOOSTER)
    status = Column(Enum(UserStatus), nullable=False, default=UserStatus.PENDING_VERIFICATION)
    avatar_url = Column(Text)
    bio = Column(Text)
    phone = Column(String(20))
    country = Column(String(100))
    timezone = Column(String(50))
    email_verified = Column(Boolean, default=False)
    phone_verified = Column(Boolean, default=False)
    rating = Column(Decimal(3, 2), default=0.00)
    total_earnings = Column(Decimal(10, 2), default=0.00)
    total_spent = Column(Decimal(10, 2), default=0.00)
    stripe_customer_id = Column(String(255))
    stripe_account_id = Column(String(255))
    oauth_provider = Column(String(50))
    oauth_id = Column(String(255))
    last_login = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<User(id={self.id}, email={self.email}, role={self.role})>"


class SocialAccount(Base):
    __tablename__ = "social_accounts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    platform = Column(Enum(SocialPlatform), nullable=False)
    account_url = Column(Text, nullable=False)
    account_username = Column(String(100), nullable=False)
    account_id = Column(String(255))
    followers_count = Column(String(50), default=0)
    verified = Column(Boolean, default=False)
    verification_token = Column(String(255))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<SocialAccount(id={self.id}, platform={self.platform}, username={self.account_username})>"
