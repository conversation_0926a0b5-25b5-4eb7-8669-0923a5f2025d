from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from decimal import Decimal
from typing import Optional
from app.core.database import get_db
from app.services.stripe_service import stripe_service
from app.services.payment_service import PaymentService
from app.utils.response import success_response, error_response
from app.utils.auth import get_current_user
from app.models.user import User
import uuid

router = APIRouter()


class DepositRequest(BaseModel):
    amount: Decimal
    payment_method_id: Optional[str] = None


class WithdrawRequest(BaseModel):
    amount: Decimal


class SetupIntentRequest(BaseModel):
    pass


@router.get("/balance")
async def get_balance(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get current user's balance and earnings"""
    try:
        payment_service = PaymentService(db)
        balance_info = await payment_service.get_user_balance(current_user.id)

        return success_response(
            data=balance_info,
            message="Balance retrieved successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Failed to get balance: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/setup-intent")
async def create_setup_intent(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Create Stripe setup intent for payment method"""
    try:
        # Ensure user has Stripe customer ID
        if not current_user.stripe_customer_id:
            customer_id = await stripe_service.create_customer(current_user)
            # Update user with customer ID (implement in user service)
        else:
            customer_id = current_user.stripe_customer_id

        setup_intent = await stripe_service.create_setup_intent(customer_id)

        return success_response(
            data=setup_intent,
            message="Setup intent created successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Failed to create setup intent: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/deposit")
async def deposit_funds(
    request: DepositRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Add funds to account (Content Creators)"""
    try:
        if current_user.role != 'content_creator':
            return error_response(
                message="Only content creators can deposit funds",
                status_code=status.HTTP_403_FORBIDDEN
            )

        payment_service = PaymentService(db)
        result = await payment_service.create_deposit(
            user_id=current_user.id,
            amount=request.amount,
            payment_method_id=request.payment_method_id
        )

        return success_response(
            data=result,
            message="Deposit initiated successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Deposit failed: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/withdraw")
async def withdraw_earnings(
    request: WithdrawRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Withdraw earnings (Content Boosters)"""
    try:
        if current_user.role != 'content_booster':
            return error_response(
                message="Only content boosters can withdraw earnings",
                status_code=status.HTTP_403_FORBIDDEN
            )

        payment_service = PaymentService(db)
        result = await payment_service.create_withdrawal(
            user_id=current_user.id,
            amount=request.amount
        )

        return success_response(
            data=result,
            message="Withdrawal processed successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Withdrawal failed: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/transactions")
async def get_transactions(
    page: int = 1,
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get payment transaction history"""
    try:
        payment_service = PaymentService(db)
        transactions = await payment_service.get_user_transactions(
            user_id=current_user.id,
            page=page,
            limit=limit
        )

        return success_response(
            data=transactions,
            message="Transactions retrieved successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Failed to get transactions: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/webhook")
async def stripe_webhook(request: Request):
    """Handle Stripe webhook events"""
    try:
        payload = await request.body()
        sig_header = request.headers.get('stripe-signature')

        if not sig_header:
            return error_response(
                message="Missing stripe-signature header",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        event_data = await stripe_service.handle_webhook(payload.decode(), sig_header)

        # Process webhook event
        payment_service = PaymentService(None)  # No DB session needed for webhook processing
        await payment_service.process_webhook_event(event_data)

        return success_response(
            data={"received": True},
            message="Webhook processed successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Webhook processing failed: {str(e)}",
            status_code=status.HTTP_400_BAD_REQUEST
        )
