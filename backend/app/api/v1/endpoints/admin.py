from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from typing import Optional
import uuid

from app.core.database import get_db
from app.utils.response import success_response, error_response
from app.utils.auth import get_current_admin
from app.services.admin_service import AdminService
from app.services.campaign_service import CampaignService
from app.models.user import User, UserRole, UserStatus
from app.models.campaign import CampaignStatus

router = APIRouter()


class UserStatusUpdate(BaseModel):
    status: UserStatus


@router.get("/analytics")
async def get_platform_analytics(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """Get comprehensive platform analytics (Admin only)"""
    try:
        admin_service = AdminService(db)
        analytics = await admin_service.get_platform_analytics()

        return success_response(
            data=analytics,
            message="Platform analytics retrieved successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Failed to get analytics: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/health")
async def get_platform_health(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """Get platform health metrics (Admin only)"""
    try:
        admin_service = AdminService(db)
        health = await admin_service.get_platform_health()

        return success_response(
            data=health,
            message="Platform health retrieved successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Failed to get platform health: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/users")
async def get_all_users(
    role: Optional[UserRole] = Query(None, description="Filter by user role"),
    status: Optional[UserStatus] = Query(None, description="Filter by user status"),
    search: Optional[str] = Query(None, description="Search by email, username, or name"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """Get all users with filtering (Admin only)"""
    try:
        admin_service = AdminService(db)
        users = await admin_service.get_users(
            role=role,
            status=status,
            search=search,
            page=page,
            limit=limit
        )

        return success_response(
            data=users,
            message="Users retrieved successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Failed to get users: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.put("/users/{user_id}/status")
async def update_user_status(
    user_id: str,
    status_update: UserStatusUpdate,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """Update user status (Admin only)"""
    try:
        admin_service = AdminService(db)
        success = await admin_service.update_user_status(
            user_id=uuid.UUID(user_id),
            new_status=status_update.status,
            admin_id=current_user.id
        )

        if not success:
            return error_response(
                message="Failed to update user status",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        return success_response(
            data={"updated": True, "new_status": status_update.status},
            message="User status updated successfully"
        )
    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        return error_response(
            message=f"Failed to update user status: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/campaigns")
async def get_all_campaigns(
    status: Optional[CampaignStatus] = Query(None, description="Filter by campaign status"),
    creator_id: Optional[str] = Query(None, description="Filter by creator ID"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """Get all campaigns (Admin only)"""
    try:
        campaign_service = CampaignService(db)

        # Convert creator_id string to UUID if provided
        creator_uuid = uuid.UUID(creator_id) if creator_id else None

        campaigns = await campaign_service.get_campaigns(
            creator_id=creator_uuid,
            status=status,
            page=page,
            limit=limit
        )

        return success_response(
            data=campaigns,
            message="Campaigns retrieved successfully"
        )
    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        return error_response(
            message=f"Failed to get campaigns: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/suspicious-activities")
async def get_suspicious_activities(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """Get suspicious activities for fraud detection (Admin only)"""
    try:
        admin_service = AdminService(db)
        activities = await admin_service.get_suspicious_activities(
            page=page,
            limit=limit
        )

        return success_response(
            data=activities,
            message="Suspicious activities retrieved successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Failed to get suspicious activities: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/audit-logs")
async def get_audit_logs(
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    action: Optional[str] = Query(None, description="Filter by action"),
    resource_type: Optional[str] = Query(None, description="Filter by resource type"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """Get audit logs (Admin only)"""
    try:
        admin_service = AdminService(db)

        # Convert user_id string to UUID if provided
        user_uuid = uuid.UUID(user_id) if user_id else None

        logs = await admin_service.get_audit_logs(
            user_id=user_uuid,
            action=action,
            resource_type=resource_type,
            page=page,
            limit=limit
        )

        return success_response(
            data=logs,
            message="Audit logs retrieved successfully"
        )
    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        return error_response(
            message=f"Failed to get audit logs: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/transactions")
async def get_all_transactions(
    status: Optional[str] = Query(None, description="Filter by transaction status"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_db)
):
    """Get all transactions (Admin only)"""
    try:
        # This would use a transaction service similar to others
        # For now, return a placeholder
        return success_response(
            data={
                "message": "Transaction monitoring endpoint",
                "note": "Would integrate with payment service to show all platform transactions"
            },
            message="Transactions retrieved successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Failed to get transactions: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
