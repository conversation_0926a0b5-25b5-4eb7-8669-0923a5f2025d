from fastapi import APIRouter, Depends, status
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.utils.response import success_response, error_response

router = APIRouter()


@router.get("/profile")
async def get_profile(db: AsyncSession = Depends(get_db)):
    """Get current user profile"""
    return success_response(
        data={"message": "Profile endpoint - to be implemented"},
        message="Profile retrieved successfully"
    )


@router.put("/profile")
async def update_profile(db: AsyncSession = Depends(get_db)):
    """Update current user profile"""
    return success_response(
        data={"message": "Profile update endpoint - to be implemented"},
        message="Profile updated successfully"
    )


@router.get("/social-accounts")
async def get_social_accounts(db: AsyncSession = Depends(get_db)):
    """Get user's social media accounts"""
    return success_response(
        data={"message": "Social accounts endpoint - to be implemented"},
        message="Social accounts retrieved successfully"
    )


@router.post("/social-accounts")
async def create_social_account(db: AsyncSession = Depends(get_db)):
    """Connect a new social media account"""
    return success_response(
        data={"message": "Social account creation endpoint - to be implemented"},
        message="Social account connected successfully"
    )
