from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel
from typing import Optional
import uuid

from app.core.database import get_db
from app.utils.response import success_response, error_response
from app.utils.auth import get_current_user, get_current_content_creator, get_current_content_booster
from app.services.task_service import TaskService
from app.models.user import User
from app.models.task import TaskStatus

router = APIRouter()


class TaskSubmissionRequest(BaseModel):
    proof_url: Optional[str] = None
    proof_description: Optional[str] = None
    completion_notes: Optional[str] = None


class TaskRejectionRequest(BaseModel):
    rejection_reason: str


@router.get("/")
async def get_tasks(
    status: Optional[TaskStatus] = Query(None, description="Filter by task status"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get tasks for current user"""
    try:
        task_service = TaskService(db)
        tasks = await task_service.get_user_tasks(
            user_id=current_user.id,
            status=status,
            page=page,
            limit=limit
        )

        return success_response(
            data=tasks,
            message="Tasks retrieved successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Failed to get tasks: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/available")
async def get_available_tasks(
    platform: Optional[str] = Query(None, description="Filter by platform"),
    engagement_type: Optional[str] = Query(None, description="Filter by engagement type"),
    min_price: Optional[float] = Query(None, description="Minimum price per task"),
    max_price: Optional[float] = Query(None, description="Maximum price per task"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_content_booster),
    db: AsyncSession = Depends(get_db)
):
    """Get available tasks for content boosters"""
    try:
        task_service = TaskService(db)
        tasks = await task_service.get_available_tasks(
            booster_id=current_user.id,
            platform=platform,
            engagement_type=engagement_type,
            min_price=min_price,
            max_price=max_price,
            page=page,
            limit=limit
        )

        return success_response(
            data=tasks,
            message="Available tasks retrieved successfully"
        )
    except Exception as e:
        return error_response(
            message=f"Failed to get available tasks: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.get("/{task_id}")
async def get_task(
    task_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get task details"""
    try:
        task_service = TaskService(db)
        task = await task_service.get_task_by_id(uuid.UUID(task_id))

        if not task:
            return error_response(
                message="Task not found",
                status_code=status.HTTP_404_NOT_FOUND
            )

        return success_response(
            data=task,
            message="Task details retrieved successfully"
        )
    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        return error_response(
            message=f"Failed to get task: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/{task_id}/claim")
async def claim_task(
    task_id: str,
    current_user: User = Depends(get_current_content_booster),
    db: AsyncSession = Depends(get_db)
):
    """Claim an available task (Content Boosters only)"""
    try:
        task_service = TaskService(db)
        success = await task_service.claim_task(uuid.UUID(task_id), current_user.id)

        if not success:
            return error_response(
                message="Failed to claim task",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        return success_response(
            data={"claimed": True},
            message="Task claimed successfully"
        )
    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        return error_response(
            message=f"Failed to claim task: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/{task_id}/submit")
async def submit_task(
    task_id: str,
    submission: TaskSubmissionRequest,
    current_user: User = Depends(get_current_content_booster),
    db: AsyncSession = Depends(get_db)
):
    """Submit task completion with proof (Content Boosters only)"""
    try:
        task_service = TaskService(db)
        success = await task_service.submit_task(
            task_id=uuid.UUID(task_id),
            booster_id=current_user.id,
            proof_url=submission.proof_url,
            proof_description=submission.proof_description,
            completion_notes=submission.completion_notes
        )

        if not success:
            return error_response(
                message="Failed to submit task",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        return success_response(
            data={"submitted": True},
            message="Task submitted successfully"
        )
    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        return error_response(
            message=f"Failed to submit task: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/{task_id}/approve")
async def approve_task(
    task_id: str,
    current_user: User = Depends(get_current_content_creator),
    db: AsyncSession = Depends(get_db)
):
    """Approve completed task (Content Creators only)"""
    try:
        task_service = TaskService(db)
        success = await task_service.approve_task(uuid.UUID(task_id), current_user.id)

        if not success:
            return error_response(
                message="Failed to approve task",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        return success_response(
            data={"approved": True},
            message="Task approved successfully"
        )
    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        return error_response(
            message=f"Failed to approve task: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/{task_id}/reject")
async def reject_task(
    task_id: str,
    rejection: TaskRejectionRequest,
    current_user: User = Depends(get_current_content_creator),
    db: AsyncSession = Depends(get_db)
):
    """Reject completed task with reason (Content Creators only)"""
    try:
        task_service = TaskService(db)
        success = await task_service.reject_task(
            task_id=uuid.UUID(task_id),
            creator_id=current_user.id,
            rejection_reason=rejection.rejection_reason
        )

        if not success:
            return error_response(
                message="Failed to reject task",
                status_code=status.HTTP_400_BAD_REQUEST
            )

        return success_response(
            data={"rejected": True},
            message="Task rejected successfully"
        )
    except ValueError as e:
        return error_response(
            message=str(e),
            status_code=status.HTTP_400_BAD_REQUEST
        )
    except Exception as e:
        return error_response(
            message=f"Failed to reject task: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
