from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import get_db
from app.core.security import (
    verify_password, get_password_hash, create_access_token, 
    create_refresh_token, verify_token, create_email_verification_token
)
from app.schemas.user import UserCreate, UserLogin, AuthResponse, TokenResponse
from app.services.user_service import UserService
from app.utils.response import success_response, error_response
import time

router = APIRouter()
security = HTTPBearer()


@router.post("/register", response_model=dict)
async def register(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db)
):
    """Register a new user"""
    try:
        user_service = UserService(db)
        
        # Check if user already exists
        existing_user = await user_service.get_user_by_email(user_data.email)
        if existing_user:
            return error_response(
                message="User with this email already exists",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        existing_username = await user_service.get_user_by_username(user_data.username)
        if existing_username:
            return error_response(
                message="Username already taken",
                status_code=status.HTTP_400_BAD_REQUEST
            )
        
        # Create user
        user = await user_service.create_user(user_data)
        
        # Create tokens
        access_token = create_access_token({"sub": str(user.id), "email": user.email})
        refresh_token = create_refresh_token({"sub": str(user.id), "email": user.email})
        
        # Send verification email (implement later)
        # await send_verification_email(user.email, create_email_verification_token(user.email))
        
        return success_response(
            data={
                "user": {
                    "id": str(user.id),
                    "email": user.email,
                    "username": user.username,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "role": user.role,
                    "status": user.status,
                    "email_verified": user.email_verified
                },
                "tokens": {
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "token_type": "bearer",
                    "expires_in": 1800  # 30 minutes
                }
            },
            message="User registered successfully"
        )
        
    except Exception as e:
        return error_response(
            message=f"Registration failed: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/login", response_model=dict)
async def login(
    credentials: UserLogin,
    db: AsyncSession = Depends(get_db)
):
    """Authenticate user and return tokens"""
    try:
        user_service = UserService(db)
        
        # Get user by email
        user = await user_service.get_user_by_email(credentials.email)
        if not user:
            return error_response(
                message="Invalid email or password",
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        
        # Verify password
        if not verify_password(credentials.password, user.password_hash):
            return error_response(
                message="Invalid email or password",
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        
        # Check if user is active
        if user.status == "banned":
            return error_response(
                message="Account has been banned",
                status_code=status.HTTP_403_FORBIDDEN
            )
        
        if user.status == "suspended":
            return error_response(
                message="Account has been suspended",
                status_code=status.HTTP_403_FORBIDDEN
            )
        
        # Update last login
        await user_service.update_last_login(user.id)
        
        # Create tokens
        access_token = create_access_token({"sub": str(user.id), "email": user.email})
        refresh_token = create_refresh_token({"sub": str(user.id), "email": user.email})
        
        return success_response(
            data={
                "user": {
                    "id": str(user.id),
                    "email": user.email,
                    "username": user.username,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "role": user.role,
                    "status": user.status,
                    "email_verified": user.email_verified,
                    "avatar_url": user.avatar_url
                },
                "tokens": {
                    "access_token": access_token,
                    "refresh_token": refresh_token,
                    "token_type": "bearer",
                    "expires_in": 1800  # 30 minutes
                }
            },
            message="Login successful"
        )
        
    except Exception as e:
        return error_response(
            message=f"Login failed: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/refresh", response_model=dict)
async def refresh_token(
    refresh_token: str,
    db: AsyncSession = Depends(get_db)
):
    """Refresh access token using refresh token"""
    try:
        # Verify refresh token
        payload = verify_token(refresh_token)
        if not payload or payload.get("type") != "refresh":
            return error_response(
                message="Invalid refresh token",
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        
        user_id = payload.get("sub")
        email = payload.get("email")
        
        if not user_id or not email:
            return error_response(
                message="Invalid token payload",
                status_code=status.HTTP_401_UNAUTHORIZED
            )
        
        # Create new access token
        access_token = create_access_token({"sub": user_id, "email": email})
        
        return success_response(
            data={
                "access_token": access_token,
                "token_type": "bearer",
                "expires_in": 1800  # 30 minutes
            },
            message="Token refreshed successfully"
        )
        
    except Exception as e:
        return error_response(
            message=f"Token refresh failed: {str(e)}",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@router.post("/logout", response_model=dict)
async def logout():
    """Logout user (client should discard tokens)"""
    return success_response(
        message="Logged out successfully",
        data={}
    )
