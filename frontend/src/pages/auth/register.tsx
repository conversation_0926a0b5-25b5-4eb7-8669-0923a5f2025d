import { useState } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion } from 'framer-motion';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { useAuth } from '@/hooks/useAuth';
import { RegisterData } from '@/types';

const registerSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  first_name: z.string().min(1, 'First name is required').max(100, 'First name is too long'),
  last_name: z.string().min(1, 'Last name is required').max(100, 'Last name is too long'),
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username is too long')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),
  role: z.enum(['content_creator', 'content_booster']),
});

export default function Register() {
  const [showPassword, setShowPassword] = useState(false);
  const { register: registerUser, loading } = useAuth();
  const router = useRouter();

  // Get role from query params
  const defaultRole = router.query.role === 'content_creator' ? 'content_creator' : 'content_booster';

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
  } = useForm<RegisterData>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      role: defaultRole,
    },
  });

  const selectedRole = watch('role');

  const onSubmit = async (data: RegisterData) => {
    try {
      await registerUser(data);
    } catch (error) {
      // Error is handled by the auth hook
    }
  };

  return (
    <>
      <Head>
        <title>Register - Social Engagement Platform</title>
        <meta name="description" content="Create your account" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-md w-full space-y-8"
        >
          <div>
            <Link href="/" className="flex justify-center">
              <h1 className="text-3xl font-bold text-primary-600">SocialBoost</h1>
            </Link>
            <h2 className="mt-6 text-center text-3xl font-bold text-secondary-900">
              Create your account
            </h2>
            <p className="mt-2 text-center text-sm text-secondary-600">
              Already have an account?{' '}
              <Link href="/auth/login" className="font-medium text-primary-600 hover:text-primary-500">
                Sign in
              </Link>
            </p>
          </div>

          <div className="card">
            <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
              {/* Role Selection */}
              <div>
                <label className="block text-sm font-medium text-secondary-700 mb-3">
                  I want to:
                </label>
                <div className="grid grid-cols-1 gap-3">
                  <label className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                    selectedRole === 'content_creator' 
                      ? 'border-primary-600 bg-primary-50' 
                      : 'border-secondary-300 bg-white hover:bg-secondary-50'
                  }`}>
                    <input
                      {...register('role')}
                      type="radio"
                      value="content_creator"
                      className="sr-only"
                    />
                    <div className="flex flex-1">
                      <div className="flex flex-col">
                        <span className="block text-sm font-medium text-secondary-900">
                          Create Campaigns
                        </span>
                        <span className="block text-sm text-secondary-500">
                          I want to boost my social media content
                        </span>
                      </div>
                    </div>
                  </label>
                  
                  <label className={`relative flex cursor-pointer rounded-lg border p-4 focus:outline-none ${
                    selectedRole === 'content_booster' 
                      ? 'border-primary-600 bg-primary-50' 
                      : 'border-secondary-300 bg-white hover:bg-secondary-50'
                  }`}>
                    <input
                      {...register('role')}
                      type="radio"
                      value="content_booster"
                      className="sr-only"
                    />
                    <div className="flex flex-1">
                      <div className="flex flex-col">
                        <span className="block text-sm font-medium text-secondary-900">
                          Complete Tasks
                        </span>
                        <span className="block text-sm text-secondary-500">
                          I want to earn money by engaging with content
                        </span>
                      </div>
                    </div>
                  </label>
                </div>
              </div>

              {/* Name Fields */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="first_name" className="block text-sm font-medium text-secondary-700">
                    First name
                  </label>
                  <input
                    {...register('first_name')}
                    type="text"
                    className={`mt-1 input-field ${errors.first_name ? 'border-error-500 focus:ring-error-500 focus:border-error-500' : ''}`}
                    placeholder="John"
                  />
                  {errors.first_name && (
                    <p className="mt-1 text-sm text-error-600">{errors.first_name.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="last_name" className="block text-sm font-medium text-secondary-700">
                    Last name
                  </label>
                  <input
                    {...register('last_name')}
                    type="text"
                    className={`mt-1 input-field ${errors.last_name ? 'border-error-500 focus:ring-error-500 focus:border-error-500' : ''}`}
                    placeholder="Doe"
                  />
                  {errors.last_name && (
                    <p className="mt-1 text-sm text-error-600">{errors.last_name.message}</p>
                  )}
                </div>
              </div>

              {/* Username */}
              <div>
                <label htmlFor="username" className="block text-sm font-medium text-secondary-700">
                  Username
                </label>
                <input
                  {...register('username')}
                  type="text"
                  className={`mt-1 input-field ${errors.username ? 'border-error-500 focus:ring-error-500 focus:border-error-500' : ''}`}
                  placeholder="johndoe"
                />
                {errors.username && (
                  <p className="mt-1 text-sm text-error-600">{errors.username.message}</p>
                )}
              </div>

              {/* Email */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-secondary-700">
                  Email address
                </label>
                <input
                  {...register('email')}
                  type="email"
                  autoComplete="email"
                  className={`mt-1 input-field ${errors.email ? 'border-error-500 focus:ring-error-500 focus:border-error-500' : ''}`}
                  placeholder="<EMAIL>"
                />
                {errors.email && (
                  <p className="mt-1 text-sm text-error-600">{errors.email.message}</p>
                )}
              </div>

              {/* Password */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-secondary-700">
                  Password
                </label>
                <div className="mt-1 relative">
                  <input
                    {...register('password')}
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    className={`input-field pr-10 ${errors.password ? 'border-error-500 focus:ring-error-500 focus:border-error-500' : ''}`}
                    placeholder="Create a strong password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-secondary-400" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-secondary-400" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-error-600">{errors.password.message}</p>
                )}
                <p className="mt-1 text-xs text-secondary-500">
                  Must be at least 8 characters with uppercase, lowercase, and number
                </p>
              </div>

              <div>
                <button
                  type="submit"
                  disabled={loading}
                  className="w-full btn-primary flex justify-center items-center"
                >
                  {loading ? (
                    <>
                      <div className="spinner mr-2"></div>
                      Creating account...
                    </>
                  ) : (
                    'Create account'
                  )}
                </button>
              </div>
            </form>

            <div className="mt-6">
              <p className="text-xs text-center text-secondary-500">
                By creating an account, you agree to our{' '}
                <Link href="/terms" className="text-primary-600 hover:text-primary-500">
                  Terms of Service
                </Link>{' '}
                and{' '}
                <Link href="/privacy" className="text-primary-600 hover:text-primary-500">
                  Privacy Policy
                </Link>
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </>
  );
}
