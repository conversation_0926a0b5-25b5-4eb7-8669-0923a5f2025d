import Head from 'next/head';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { 
  PlayIcon, 
  HeartIcon, 
  ChatBubbleLeftIcon,
  ShareIcon,
  EyeIcon,
  CurrencyDollarIcon,
  ShieldCheckIcon,
  ClockIcon
} from '@heroicons/react/24/outline';

export default function Home() {
  const features = [
    {
      icon: CurrencyDollarIcon,
      title: 'Earn Money',
      description: 'Complete engagement tasks and earn rewards for your social media activities.',
    },
    {
      icon: ShieldCheckIcon,
      title: 'Secure Payments',
      description: 'Escrow-protected transactions ensure safe and reliable payments for all users.',
    },
    {
      icon: ClockIcon,
      title: 'Flexible Schedule',
      description: 'Work on your own time and choose campaigns that fit your schedule.',
    },
  ];

  const engagementTypes = [
    { icon: HeartIcon, name: 'Likes', color: 'text-red-500' },
    { icon: ChatBubbleLeftIcon, name: 'Comments', color: 'text-blue-500' },
    { icon: ShareIcon, name: 'Shares', color: 'text-green-500' },
    { icon: EyeIcon, name: 'Views', color: 'text-purple-500' },
    { icon: PlayIcon, name: 'Watch Time', color: 'text-orange-500' },
  ];

  return (
    <>
      <Head>
        <title>Social Engagement Platform - Boost Your Social Media</title>
        <meta name="description" content="Connect content creators with content boosters for authentic social media engagement" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-100">
        {/* Navigation */}
        <nav className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center h-16">
              <div className="flex items-center">
                <h1 className="text-2xl font-bold text-primary-600">SocialBoost</h1>
              </div>
              <div className="flex items-center space-x-4">
                <Link href="/auth/login" className="text-secondary-600 hover:text-primary-600 font-medium">
                  Login
                </Link>
                <Link href="/auth/register" className="btn-primary">
                  Get Started
                </Link>
              </div>
            </div>
          </div>
        </nav>

        {/* Hero Section */}
        <section className="py-20 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h1 className="text-4xl md:text-6xl font-bold text-secondary-900 mb-6">
                Boost Your Social Media
                <span className="text-primary-600 block">Engagement</span>
              </h1>
              <p className="text-xl text-secondary-600 mb-8 max-w-3xl mx-auto">
                Connect content creators with content boosters for authentic social media engagement. 
                Grow your audience or earn money by completing engagement tasks.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/auth/register?role=content_creator" className="btn-primary text-lg px-8 py-3">
                  I'm a Content Creator
                </Link>
                <Link href="/auth/register?role=content_booster" className="btn-outline text-lg px-8 py-3">
                  I'm a Content Booster
                </Link>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Engagement Types */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-secondary-900 mb-4">
                Multiple Engagement Types
              </h2>
              <p className="text-lg text-secondary-600">
                Support various social media platforms and engagement types
              </p>
            </div>
            <div className="flex flex-wrap justify-center gap-8">
              {engagementTypes.map((type, index) => (
                <motion.div
                  key={type.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="flex flex-col items-center p-6 rounded-lg bg-secondary-50 hover:bg-secondary-100 transition-colors"
                >
                  <type.icon className={`h-12 w-12 ${type.color} mb-3`} />
                  <span className="font-medium text-secondary-900">{type.name}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Features */}
        <section className="py-16 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-secondary-900 mb-4">
                Why Choose Our Platform?
              </h2>
              <p className="text-lg text-secondary-600">
                Built for creators and boosters with security and ease of use in mind
              </p>
            </div>
            <div className="grid md:grid-cols-3 gap-8">
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.2 }}
                  className="card text-center"
                >
                  <feature.icon className="h-12 w-12 text-primary-600 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-secondary-900 mb-3">
                    {feature.title}
                  </h3>
                  <p className="text-secondary-600">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 px-4 sm:px-6 lg:px-8 bg-primary-600">
          <div className="max-w-4xl mx-auto text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
            >
              <h2 className="text-3xl font-bold text-white mb-4">
                Ready to Get Started?
              </h2>
              <p className="text-xl text-primary-100 mb-8">
                Join thousands of creators and boosters already using our platform
              </p>
              <Link href="/auth/register" className="bg-white text-primary-600 hover:bg-primary-50 font-semibold py-3 px-8 rounded-lg transition-colors">
                Sign Up Now
              </Link>
            </motion.div>
          </div>
        </section>

        {/* Footer */}
        <footer className="bg-secondary-900 text-white py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-7xl mx-auto text-center">
            <h3 className="text-2xl font-bold mb-4">SocialBoost</h3>
            <p className="text-secondary-400 mb-6">
              Connecting creators and boosters for authentic social media growth
            </p>
            <div className="flex justify-center space-x-6">
              <Link href="/privacy" className="text-secondary-400 hover:text-white">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-secondary-400 hover:text-white">
                Terms of Service
              </Link>
              <Link href="/contact" className="text-secondary-400 hover:text-white">
                Contact
              </Link>
            </div>
          </div>
        </footer>
      </div>
    </>
  );
}
