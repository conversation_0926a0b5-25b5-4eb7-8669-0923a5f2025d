// User types
export interface User {
  id: string;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  role: 'content_creator' | 'content_booster' | 'admin';
  status: 'active' | 'suspended' | 'banned' | 'pending_verification';
  avatar_url?: string;
  bio?: string;
  phone?: string;
  country?: string;
  timezone?: string;
  email_verified: boolean;
  phone_verified: boolean;
  rating: number;
  total_earnings: number;
  total_spent: number;
  created_at: string;
  updated_at: string;
}

// Social Media types
export type SocialPlatform = 'youtube' | 'tiktok' | 'instagram' | 'facebook' | 'twitter' | 'linkedin';

export interface SocialAccount {
  id: string;
  user_id: string;
  platform: SocialPlatform;
  account_url: string;
  account_username: string;
  account_id?: string;
  followers_count: number;
  verified: boolean;
  created_at: string;
  updated_at: string;
}

// Campaign types
export type EngagementType = 'likes' | 'comments' | 'shares' | 'views' | 'watch_time' | 'subscribers' | 'follows';
export type CampaignStatus = 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';

export interface Campaign {
  id: string;
  creator_id: string;
  social_account_id: string;
  title: string;
  description?: string;
  platform: SocialPlatform;
  engagement_type: EngagementType;
  target_url: string;
  target_quantity: number;
  completed_quantity: number;
  price_per_task: number;
  total_budget: number;
  remaining_budget: number;
  status: CampaignStatus;
  start_date?: string;
  end_date?: string;
  max_tasks_per_user: number;
  min_booster_rating: number;
  instructions?: string;
  proof_required: boolean;
  auto_approve: boolean;
  created_at: string;
  updated_at: string;
}

// Task types
export type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'rejected' | 'disputed';

export interface Task {
  id: string;
  campaign_id: string;
  booster_id?: string;
  status: TaskStatus;
  proof_url?: string;
  proof_description?: string;
  completion_notes?: string;
  assigned_at?: string;
  completed_at?: string;
  reviewed_at?: string;
  reviewer_id?: string;
  rejection_reason?: string;
  created_at: string;
  updated_at: string;
}

// Transaction types
export type TransactionStatus = 'pending' | 'held' | 'completed' | 'refunded' | 'disputed';

export interface Transaction {
  id: string;
  campaign_id: string;
  task_id?: string;
  creator_id: string;
  booster_id?: string;
  amount: number;
  fee: number;
  net_amount: number;
  status: TransactionStatus;
  stripe_payment_intent_id?: string;
  stripe_transfer_id?: string;
  escrow_released_at?: string;
  refunded_at?: string;
  refund_reason?: string;
  created_at: string;
  updated_at: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message: string;
  errors: string[];
  meta: {
    timestamp: number;
    request_id?: string;
  };
}

export interface PaginatedResponse<T = any> {
  items: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    has_next: boolean;
    has_prev: boolean;
    total_pages: number;
  };
}

// Auth types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  username: string;
  role: 'content_creator' | 'content_booster';
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export interface AuthResponse {
  user: User;
  tokens: AuthTokens;
}

// Form types
export interface CampaignFormData {
  social_account_id: string;
  title: string;
  description?: string;
  platform: SocialPlatform;
  engagement_type: EngagementType;
  target_url: string;
  target_quantity: number;
  price_per_task: number;
  end_date?: string;
  max_tasks_per_user: number;
  min_booster_rating: number;
  instructions?: string;
  proof_required: boolean;
  auto_approve: boolean;
}

// Notification types
export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: string;
  read: boolean;
  data?: any;
  created_at: string;
}
