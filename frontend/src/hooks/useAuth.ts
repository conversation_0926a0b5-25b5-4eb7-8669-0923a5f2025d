import { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { useRouter } from 'next/router';
import Cookies from 'js-cookie';
import { toast } from 'react-hot-toast';
import { apiService } from '@/services/api';
import { User, LoginCredentials, RegisterData, AuthResponse } from '@/types';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
  isAuthenticated: boolean;
  isContentCreator: boolean;
  isContentBooster: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Check if user is authenticated
  const isAuthenticated = !!user;
  const isContentCreator = user?.role === 'content_creator';
  const isContentBooster = user?.role === 'content_booster';
  const isAdmin = user?.role === 'admin';

  // Initialize auth state
  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      const token = Cookies.get('access_token');
      if (token) {
        await refreshUser();
      }
    } catch (error) {
      console.error('Auth initialization error:', error);
      // Clear invalid tokens
      Cookies.remove('access_token');
      Cookies.remove('refresh_token');
    } finally {
      setLoading(false);
    }
  };

  const login = async (credentials: LoginCredentials) => {
    try {
      setLoading(true);
      const response: AuthResponse = await apiService.login(credentials.email, credentials.password);
      
      if (response.user && response.tokens) {
        // Store tokens
        Cookies.set('access_token', response.tokens.access_token, { expires: 1 });
        Cookies.set('refresh_token', response.tokens.refresh_token, { expires: 7 });
        
        // Set user
        setUser(response.user);
        
        toast.success('Login successful!');
        
        // Redirect based on role
        if (response.user.role === 'content_creator') {
          router.push('/creator/dashboard');
        } else if (response.user.role === 'content_booster') {
          router.push('/booster/dashboard');
        } else if (response.user.role === 'admin') {
          router.push('/admin/dashboard');
        } else {
          router.push('/dashboard');
        }
      }
    } catch (error: any) {
      console.error('Login error:', error);
      toast.error(error.response?.data?.message || 'Login failed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (data: RegisterData) => {
    try {
      setLoading(true);
      const response: AuthResponse = await apiService.register(data);
      
      if (response.user && response.tokens) {
        // Store tokens
        Cookies.set('access_token', response.tokens.access_token, { expires: 1 });
        Cookies.set('refresh_token', response.tokens.refresh_token, { expires: 7 });
        
        // Set user
        setUser(response.user);
        
        toast.success('Registration successful!');
        
        // Redirect to appropriate dashboard
        if (response.user.role === 'content_creator') {
          router.push('/creator/dashboard');
        } else {
          router.push('/booster/dashboard');
        }
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      toast.error(error.response?.data?.message || 'Registration failed');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await apiService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear state and cookies
      setUser(null);
      Cookies.remove('access_token');
      Cookies.remove('refresh_token');
      
      toast.success('Logged out successfully');
      router.push('/');
    }
  };

  const refreshUser = async () => {
    try {
      const response = await apiService.getProfile();
      if (response.success && response.data) {
        setUser(response.data);
      }
    } catch (error) {
      console.error('Refresh user error:', error);
      // If refresh fails, clear auth state
      setUser(null);
      Cookies.remove('access_token');
      Cookies.remove('refresh_token');
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    refreshUser,
    isAuthenticated,
    isContentCreator,
    isContentBooster,
    isAdmin,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
