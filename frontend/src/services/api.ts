import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';
import { toast } from 'react-hot-toast';
import Cookies from 'js-cookie';
import { ApiResponse } from '@/types';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    this.baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';
    
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = Cookies.get('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      async (error: AxiosError) => {
        const originalRequest = error.config as any;

        // Handle 401 errors (unauthorized)
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const refreshToken = Cookies.get('refresh_token');
            if (refreshToken) {
              const response = await this.refreshToken(refreshToken);
              const { access_token } = response.data;
              
              Cookies.set('access_token', access_token, { expires: 1 });
              originalRequest.headers.Authorization = `Bearer ${access_token}`;
              
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            // Refresh failed, redirect to login
            this.logout();
            window.location.href = '/auth/login';
          }
        }

        // Handle other errors
        if (error.response?.data) {
          const errorData = error.response.data as ApiResponse;
          if (errorData.message) {
            toast.error(errorData.message);
          }
        } else if (error.message) {
          toast.error(error.message);
        }

        return Promise.reject(error);
      }
    );
  }

  // Auth methods
  async login(email: string, password: string) {
    const response = await this.api.post('/auth/login', { email, password });
    return response.data;
  }

  async register(userData: any) {
    const response = await this.api.post('/auth/register', userData);
    return response.data;
  }

  async refreshToken(refreshToken: string) {
    const response = await this.api.post('/auth/refresh', { refresh_token: refreshToken });
    return response.data;
  }

  async logout() {
    try {
      await this.api.post('/auth/logout');
    } catch (error) {
      // Ignore errors on logout
    } finally {
      Cookies.remove('access_token');
      Cookies.remove('refresh_token');
    }
  }

  // User methods
  async getProfile() {
    const response = await this.api.get('/users/profile');
    return response.data;
  }

  async updateProfile(userData: any) {
    const response = await this.api.put('/users/profile', userData);
    return response.data;
  }

  async getSocialAccounts() {
    const response = await this.api.get('/users/social-accounts');
    return response.data;
  }

  async createSocialAccount(accountData: any) {
    const response = await this.api.post('/users/social-accounts', accountData);
    return response.data;
  }

  // Campaign methods
  async getCampaigns(params?: any) {
    const response = await this.api.get('/campaigns', { params });
    return response.data;
  }

  async getCampaign(id: string) {
    const response = await this.api.get(`/campaigns/${id}`);
    return response.data;
  }

  async createCampaign(campaignData: any) {
    const response = await this.api.post('/campaigns', campaignData);
    return response.data;
  }

  async updateCampaign(id: string, campaignData: any) {
    const response = await this.api.put(`/campaigns/${id}`, campaignData);
    return response.data;
  }

  async deleteCampaign(id: string) {
    const response = await this.api.delete(`/campaigns/${id}`);
    return response.data;
  }

  // Task methods
  async getTasks(params?: any) {
    const response = await this.api.get('/tasks', { params });
    return response.data;
  }

  async claimTask(taskId: string) {
    const response = await this.api.post(`/tasks/${taskId}/claim`);
    return response.data;
  }

  async submitTask(taskId: string, submissionData: any) {
    const response = await this.api.post(`/tasks/${taskId}/submit`, submissionData);
    return response.data;
  }

  async approveTask(taskId: string) {
    const response = await this.api.post(`/tasks/${taskId}/approve`);
    return response.data;
  }

  async rejectTask(taskId: string, reason: string) {
    const response = await this.api.post(`/tasks/${taskId}/reject`, { rejection_reason: reason });
    return response.data;
  }

  // Payment methods
  async getBalance() {
    const response = await this.api.get('/payments/balance');
    return response.data;
  }

  async depositFunds(amount: number) {
    const response = await this.api.post('/payments/deposit', { amount });
    return response.data;
  }

  async withdrawEarnings(amount: number) {
    const response = await this.api.post('/payments/withdraw', { amount });
    return response.data;
  }

  async getTransactions(params?: any) {
    const response = await this.api.get('/payments/transactions', { params });
    return response.data;
  }

  // File upload
  async uploadFile(file: File, type: string = 'general') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const response = await this.api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Generic methods
  async get(endpoint: string, params?: any) {
    const response = await this.api.get(endpoint, { params });
    return response.data;
  }

  async post(endpoint: string, data?: any) {
    const response = await this.api.post(endpoint, data);
    return response.data;
  }

  async put(endpoint: string, data?: any) {
    const response = await this.api.put(endpoint, data);
    return response.data;
  }

  async delete(endpoint: string) {
    const response = await this.api.delete(endpoint);
    return response.data;
  }
}

export const apiService = new ApiService();
export default apiService;
