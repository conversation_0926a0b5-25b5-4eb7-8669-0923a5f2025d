version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: social_postgres
    environment:
      POSTGRES_DB: social_engagement
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql
    networks:
      - social_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: social_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - social_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: social_backend
    environment:
      - DATABASE_URL=********************************************/social_engagement
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
      - STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
      - STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
      - EMAIL_HOST=smtp.gmail.com
      - EMAIL_PORT=587
      - EMAIL_USER=<EMAIL>
      - EMAIL_PASSWORD=your-app-password
      - FRONTEND_URL=http://localhost:3000
      - BACKEND_URL=http://localhost:8000
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - social_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Next.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: social_frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
      - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
      - NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-oauth-client-id
      - NEXT_PUBLIC_FACEBOOK_APP_ID=your-facebook-app-id
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    depends_on:
      - backend
    networks:
      - social_network
    command: npm run dev

  # Nginx Reverse Proxy (for production)
  nginx:
    image: nginx:alpine
    container_name: social_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - social_network
    profiles:
      - production

  # MinIO for file storage (S3-compatible)
  minio:
    image: minio/minio:latest
    container_name: social_minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - social_network
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # Adminer for database management
  adminer:
    image: adminer:latest
    container_name: social_adminer
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - social_network
    profiles:
      - development

  # Mailhog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: social_mailhog
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    networks:
      - social_network
    profiles:
      - development

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  minio_data:
    driver: local

networks:
  social_network:
    driver: bridge

# Development profile services
# Run with: docker-compose --profile development up
# Production profile services  
# Run with: docker-compose --profile production up
