# Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
NODE_ENV=development
DEBUG=true
APP_NAME="Social Engagement Platform"
APP_VERSION=1.0.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/social_engagement
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=social_engagement
DATABASE_USER=postgres
DATABASE_PASSWORD=postgres

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# =============================================================================
# JWT & SECURITY
# =============================================================================
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production-min-32-chars
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7
PASSWORD_RESET_TOKEN_EXPIRE_HOURS=1
EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS=24

# =============================================================================
# STRIPE PAYMENT CONFIGURATION
# =============================================================================
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
STRIPE_PLATFORM_FEE_PERCENTAGE=5.0

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_FROM_NAME="Social Engagement Platform"
EMAIL_USE_TLS=true
EMAIL_USE_SSL=false

# For development, you can use Mailhog
# EMAIL_HOST=localhost
# EMAIL_PORT=1025
# EMAIL_USER=
# EMAIL_PASSWORD=
# EMAIL_USE_TLS=false

# =============================================================================
# OAUTH PROVIDERS
# =============================================================================
# Google OAuth
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret

# Facebook OAuth
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret

# GitHub OAuth (optional)
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# =============================================================================
# SOCIAL MEDIA API KEYS (for verification)
# =============================================================================
# YouTube Data API
YOUTUBE_API_KEY=your-youtube-api-key

# TikTok API (optional)
TIKTOK_CLIENT_KEY=your-tiktok-client-key
TIKTOK_CLIENT_SECRET=your-tiktok-client-secret

# Instagram Basic Display API
INSTAGRAM_CLIENT_ID=your-instagram-client-id
INSTAGRAM_CLIENT_SECRET=your-instagram-client-secret

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
# Local storage
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760  # 10MB in bytes

# AWS S3 (for production)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# MinIO (S3-compatible, for development)
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin123
MINIO_BUCKET=social-engagement
MINIO_USE_SSL=false

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your-google-oauth-client-id
NEXT_PUBLIC_FACEBOOK_APP_ID=your-facebook-app-id

# =============================================================================
# BACKEND CONFIGURATION
# =============================================================================
BACKEND_URL=http://localhost:8000
FRONTEND_URL=http://localhost:3000
API_V1_PREFIX=/api/v1
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_AUTH_REQUESTS_PER_MINUTE=5
RATE_LIMIT_UPLOAD_REQUESTS_PER_MINUTE=10

# =============================================================================
# LOGGING & MONITORING
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=json
SENTRY_DSN=your-sentry-dsn-for-error-tracking

# =============================================================================
# CELERY CONFIGURATION (for background tasks)
# =============================================================================
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================
# Set to true to enable API documentation
ENABLE_DOCS=true

# Set to true to enable database seeding with test data
ENABLE_SEEDING=false

# Set to true to enable detailed SQL logging
ENABLE_SQL_LOGGING=false

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================
# Only set these in production
DOMAIN=socialengagement.com
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/private/key.pem

# Security headers
SECURE_SSL_REDIRECT=true
SECURE_HSTS_SECONDS=31536000
SECURE_CONTENT_TYPE_NOSNIFF=true
SECURE_BROWSER_XSS_FILTER=true

# =============================================================================
# ANALYTICS & TRACKING (optional)
# =============================================================================
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
FACEBOOK_PIXEL_ID=your-facebook-pixel-id
MIXPANEL_TOKEN=your-mixpanel-token

# =============================================================================
# NOTIFICATION SERVICES (optional)
# =============================================================================
# Slack webhook for admin notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook

# Twilio for SMS notifications
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********
